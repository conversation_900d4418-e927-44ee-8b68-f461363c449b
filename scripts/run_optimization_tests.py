#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化测试运行器

简化的测试运行脚本，用于快速验证优化功能。

作者: Assistant
日期: 2024
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加当前脚本目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_config import PipelineConfig
from modular_pipeline import ModularPipeline


async def run_simple_test():
    """运行简单的优化测试"""
    print("🚀 开始简单优化测试")
    
    # 查找测试文件
    test_data_dir = Path("test_data")
    if not test_data_dir.exists():
        print("❌ 测试数据目录不存在: test_data")
        print("请运行 prepare_test_data.sh 创建测试数据")
        return False
    
    # 查找最小的测试文件
    test_files = list(test_data_dir.glob("*.jsonl"))
    if not test_files:
        print("❌ 没有找到测试文件")
        return False
    
    # 按文件大小排序，选择最小的
    test_files.sort(key=lambda x: x.stat().st_size)
    test_file = test_files[0]
    
    print(f"📁 使用测试文件: {test_file}")
    print(f"📊 文件大小: {test_file.stat().st_size / 1024:.1f} KB")
    
    # 创建输出目录
    output_dir = Path("test_output_simple")
    output_dir.mkdir(exist_ok=True)
    
    # 测试标准流水线
    print("\n1️⃣ 测试标准流水线...")
    standard_config = PipelineConfig(
        input_file=test_file,
        output_dir=output_dir / "standard" / "output",
        temp_dir=output_dir / "standard" / "temp",
        log_dir=output_dir / "standard" / "logs",
        cache_dir=output_dir / "standard" / "cache",
        enable_optimizations=False,
        brain_api_batch_size=100,
        clickhouse_batch_size=100
    )
    
    standard_pipeline = ModularPipeline(standard_config)
    
    try:
        import time
        start_time = time.time()
        standard_df, standard_errors = await standard_pipeline.run_pipeline(str(test_file))
        standard_duration = time.time() - start_time
        
        print(f"✅ 标准流水线完成")
        print(f"   执行时间: {standard_duration:.2f}秒")
        print(f"   输出行数: {len(standard_df)}")
        print(f"   错误数量: {sum(len(errors) for errors in standard_errors.values())}")
        
    except Exception as e:
        print(f"❌ 标准流水线失败: {e}")
        return False
    
    # 测试优化流水线
    print("\n2️⃣ 测试优化流水线...")
    optimized_config = PipelineConfig(
        input_file=test_file,
        output_dir=output_dir / "optimized" / "output",
        temp_dir=output_dir / "optimized" / "temp",
        log_dir=output_dir / "optimized" / "logs",
        cache_dir=output_dir / "optimized" / "cache",
        enable_optimizations=True,
        brain_api_batch_size=100,
        clickhouse_batch_size=100,
        max_concurrent_requests=2  # 降低并发数用于测试
    )
    
    optimized_pipeline = ModularPipeline(optimized_config)
    
    try:
        start_time = time.time()
        optimized_df, optimized_errors = await optimized_pipeline.run_optimized_pipeline(str(test_file))
        optimized_duration = time.time() - start_time
        
        print(f"✅ 优化流水线完成")
        print(f"   执行时间: {optimized_duration:.2f}秒")
        print(f"   输出行数: {len(optimized_df)}")
        print(f"   错误数量: {sum(len(errors) for errors in optimized_errors.values())}")
        
        # 获取优化统计信息
        if hasattr(optimized_pipeline, 'enhanced_brain_updater') and optimized_pipeline.enhanced_brain_updater:
            brain_stats = optimized_pipeline.enhanced_brain_updater.get_stats()
            print(f"   缓存命中: {brain_stats.get('cache_hits', 0)}")
            print(f"   API调用: {brain_stats.get('api_calls', 0)}")
            print(f"   去重节省: {brain_stats.get('deduplication_savings', 0)}")
        
    except Exception as e:
        print(f"❌ 优化流水线失败: {e}")
        return False
    
    # 比较结果
    print("\n3️⃣ 比较结果...")
    
    # 基本比较
    row_count_match = len(standard_df) == len(optimized_df)
    error_count_match = (
        sum(len(errors) for errors in standard_errors.values()) == 
        sum(len(errors) for errors in optimized_errors.values())
    )
    
    print(f"   行数匹配: {'✅' if row_count_match else '❌'} ({len(standard_df)} vs {len(optimized_df)})")
    print(f"   错误数匹配: {'✅' if error_count_match else '❌'}")
    
    # 性能比较
    if optimized_duration > 0:
        time_improvement = (standard_duration - optimized_duration) / standard_duration
        print(f"   时间改进: {time_improvement:.2%}")
    
    # 测试缓存效果（第二次运行）
    print("\n4️⃣ 测试缓存效果（第二次运行）...")
    
    try:
        start_time = time.time()
        cached_df, cached_errors = await optimized_pipeline.run_optimized_pipeline(str(test_file))
        cached_duration = time.time() - start_time
        
        print(f"✅ 缓存测试完成")
        print(f"   执行时间: {cached_duration:.2f}秒")
        
        if cached_duration > 0 and optimized_duration > 0:
            cache_improvement = (optimized_duration - cached_duration) / optimized_duration
            print(f"   缓存改进: {cache_improvement:.2%}")
        
        # 获取缓存统计
        if hasattr(optimized_pipeline, 'enhanced_brain_updater') and optimized_pipeline.enhanced_brain_updater:
            brain_stats = optimized_pipeline.enhanced_brain_updater.get_stats()
            total_processed = brain_stats.get('total_processed', 0)
            cache_hits = brain_stats.get('cache_hits', 0)
            if total_processed > 0:
                cache_hit_rate = cache_hits / total_processed
                print(f"   缓存命中率: {cache_hit_rate:.2%}")
        
    except Exception as e:
        print(f"❌ 缓存测试失败: {e}")
    
    # 总结
    print("\n📋 测试总结:")
    if row_count_match and error_count_match:
        print("✅ 数据一致性: 通过")
    else:
        print("❌ 数据一致性: 失败")
    
    print(f"📁 测试结果保存在: {output_dir}")
    print(f"📝 详细日志:")
    print(f"   标准流水线: {output_dir / 'standard' / 'logs'}")
    print(f"   优化流水线: {output_dir / 'optimized' / 'logs'}")
    
    return row_count_match and error_count_match


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="优化测试运行器")
    parser.add_argument(
        "--comprehensive", 
        action="store_true", 
        help="运行完整的测试框架"
    )
    
    args = parser.parse_args()
    
    if args.comprehensive:
        print("🚀 运行完整测试框架...")
        from test_optimization_framework import OptimizationTestFramework
        framework = OptimizationTestFramework()
        await framework.run_all_tests()
    else:
        print("🚀 运行简单优化测试...")
        success = await run_simple_test()
        if success:
            print("\n🎉 测试成功完成！")
            return 0
        else:
            print("\n💥 测试失败！")
            return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
