#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deduplicate and update a CSV file.

This script processes a CSV file to deduplicate entries based on 'material_id'
and updates a 'lowest_unit_price' field based on 'inchified_smiles' and 'unit_price'.

- Deduplication Logic:
  - Rows with the same 'material_id' are considered duplicates.
  - If duplicate 'material_id' rows have identical data, they are counted as simple duplicates.
  - If duplicate 'material_id' rows have different data in other fields, they are flagged as warnings,
    and the conflicting rows are saved to a separate warnings file.
  - The main output contains only the first occurrence of each 'material_id'.

- Update Logic:
  - For each group of rows with the same 'inchified_smiles', the script identifies the row with the
    lowest 'unit_price' and sets its 'lowest_unit_price' field to True. All other rows in the group
    will have this field set to False.
  - Updates the existing 'id' column with sequential values starting from 1.

Usage:
    python3 deduplicate_and_update_prices.py input.csv
    python3 deduplicate_and_update_prices.py input.csv output.csv duplicates.log warnings.csv
    python3 deduplicate_and_update_prices.py input.csv --verbose --dry-run
"""

import pandas as pd
import argparse
import os
import logging
import logging.handlers
import time
import traceback
import sys
from typing import Tuple, List, Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from abc import ABC, abstractmethod

# Script metadata
SCRIPT_NAME = "deduplicate_and_update_prices"
VERSION = "1.0.0"
DEFAULT_BATCH_SIZE = 10000

# Custom exceptions
class ProcessingError(Exception):
    """Base exception for processing errors"""
    pass

class ValidationError(ProcessingError):
    """Exception for data validation errors"""
    pass

@dataclass
class ProcessingResult:
    """Standardized result structure for processing operations"""
    success: bool
    total_processed: int
    success_count: int
    error_count: int
    warning_count: int
    error_details: List[Dict[str, Any]]
    execution_time: float

@dataclass
class ScriptConfig:
    """Configuration class for script parameters"""
    batch_size: int = DEFAULT_BATCH_SIZE
    timeout: int = 30
    log_level: str = 'INFO'
    log_dir: Optional[Path] = None
    keep_duplicates: bool = False
    price_field: str = 'unit_price'
    verbose: bool = False
    dry_run: bool = False

def setup_logging(
    log_level: str = 'INFO',
    log_dir: Optional[Path] = None,
    script_name: str = SCRIPT_NAME
) -> logging.Logger:
    """Setup standardized logging configuration"""

    # Create logger
    logger = logging.getLogger(script_name)
    logger.setLevel(getattr(logging, log_level.upper()))

    # Clear existing handlers
    logger.handlers.clear()

    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )

    # Console handler (INFO and above)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)

    # File handlers (if log_dir specified)
    if log_dir:
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)

        # Main log file (all levels)
        main_log_file = log_dir / f'{script_name}.log'
        main_handler = logging.handlers.RotatingFileHandler(
            main_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        main_handler.setLevel(logging.DEBUG)
        main_handler.setFormatter(detailed_formatter)
        logger.addHandler(main_handler)

        # Error log file (WARNING and above)
        error_log_file = log_dir / f'{script_name}_errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        error_handler.setLevel(logging.WARNING)
        error_handler.setFormatter(detailed_formatter)
        logger.addHandler(error_handler)

    return logger

def create_argument_parser() -> argparse.ArgumentParser:
    """Create standardized argument parser"""
    parser = argparse.ArgumentParser(
        description="Deduplicate CSV file and update price information",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""Examples:
  python3 {SCRIPT_NAME}.py input.csv
  python3 {SCRIPT_NAME}.py input.csv output.csv duplicates.log warnings.csv
  python3 {SCRIPT_NAME}.py input.csv --verbose --dry-run"""
    )

    # Required positional arguments (maintaining backward compatibility)
    parser.add_argument('input_file_path', help='Input CSV file path')
    parser.add_argument('output_file_path', nargs='?',
                       help='Output CSV file path (optional, defaults to input_filename_deduplicated.csv)')
    parser.add_argument('duplicates_log_path', nargs='?',
                       help='Duplicates log file path (optional, defaults to input_filename_duplicates.log)')
    parser.add_argument('warnings_file_path', nargs='?',
                       help='Warnings CSV file path (optional, defaults to input_filename_warnings.csv)')

    # Deduplication specific arguments
    parser.add_argument('--keep-duplicates', action='store_true',
                       help='Keep duplicate records instead of removing them')
    parser.add_argument('--price-field', default='unit_price',
                       help='Field name for price comparison (default: unit_price)')

    # Common optional arguments
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE,
                       help=f'Batch processing size (default: {DEFAULT_BATCH_SIZE})')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Operation timeout in seconds (default: 30)')
    parser.add_argument('--config-file', help='Configuration file path')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level (default: INFO)')
    parser.add_argument('--log-dir', help='Log directory path')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform validation without actual processing')
    parser.add_argument('--version', action='version', version=f'{SCRIPT_NAME} {VERSION}')

    return parser

class BaseProcessor(ABC):
    """Base class for all processor implementations"""

    def __init__(self, config: ScriptConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.warning_count = 0

    @abstractmethod
    def process_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """Process DataFrame and return success DataFrame and error records"""
        pass

    def process_csv(self, input_file: str, output_file: str, duplicates_log: str, warnings_file: str) -> ProcessingResult:
        """Process CSV file and save results"""
        start_time = time.time()

        try:
            # Load input data
            self.logger.info(f"Loading input file: {input_file}")
            df = pd.read_csv(input_file)
            self.logger.info(f"Loaded {len(df)} rows")

            # Process data
            success_df, all_records = self.process_dataframe(df)

            # Separate different types of records
            duplicate_records = [r for r in all_records if r.get('record_type') == 'duplicate']
            warning_records = [r for r in all_records if r.get('record_type') == 'warning']

            # Save results
            if not success_df.empty:
                success_df.to_csv(output_file, index=False)
                self.logger.info(f"Saved {len(success_df)} processed rows to {output_file}")

            # Save duplicates log
            if duplicate_records:
                with open(duplicates_log, 'w') as f:
                    f.write(f"Duplicate records found: {len(duplicate_records)}\n")
                    for record in duplicate_records:
                        f.write(f"Material ID: {record.get('material_id', 'N/A')}\n")
                self.logger.info(f"Saved {len(duplicate_records)} duplicate records to {duplicates_log}")

            # Save warnings
            if warning_records:
                warnings_df = pd.DataFrame(warning_records)
                warnings_df.to_csv(warnings_file, index=False)
                self.logger.info(f"Saved {len(warning_records)} warning records to {warnings_file}")

            execution_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                total_processed=self.processed_count,
                success_count=self.success_count,
                error_count=self.error_count,
                warning_count=self.warning_count,
                error_details=all_records,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Processing failed: {str(e)}")
            return ProcessingResult(
                success=False,
                total_processed=self.processed_count,
                success_count=self.success_count,
                error_count=self.error_count,
                warning_count=self.warning_count,
                error_details=[],
                execution_time=execution_time
            )

    def get_statistics(self) -> Dict[str, Any]:
        """Return processing statistics"""
        return {
            'total_processed': self.processed_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'warning_count': self.warning_count,
            'success_rate': self.success_count / self.processed_count if self.processed_count > 0 else 0
        }

class DeduplicationProcessor(BaseProcessor):
    """Processor for deduplication and price update operations"""

    def process_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """Process DataFrame for deduplication and price updates"""
        self.logger.info(f"Starting deduplication processing for {len(df)} rows")

        # Use the existing process_dataframe function
        processed_df, duplicate_records, warning_records = process_dataframe(df)
        
        # Combine all records with record type
        all_records = []
        all_records.extend([{**record, 'record_type': 'duplicate'} for record in duplicate_records])
        all_records.extend([{**record, 'record_type': 'warning'} for record in warning_records])

        # Update counters
        self.processed_count = len(df)
        self.success_count = len(processed_df)
        self.warning_count = len(warning_records)
        self.error_count = len(duplicate_records)

        return processed_df, all_records

def process_csv_legacy(input_file: str, output_file: str, log_file: str, warning_file: str):
    """
    Legacy function for backward compatibility
    Deduplicates and updates the given CSV file.

    Args:
        input_file: Path to the input CSV file.
        output_file: Path to write the processed data.
        log_file: Path to write the duplicate log.
        warning_file: Path to write conflicting (warning) rows.
    """
    logger = logging.getLogger(__name__)

    if not os.path.exists(input_file):
        logger.error(f"Input file not found: {input_file}")
        return

    logger.info(f"Reading data from {input_file}...")
    df = pd.read_csv(input_file)

    # --- Deduplication Step ---
    logger.info("Deduplicating data based on 'material_id'...")
    
    # Find all duplicates based on material_id
    duplicates_mask = df.duplicated(subset=['material_id'], keep=False)
    duplicate_rows = df[duplicates_mask]

    # Separate fully identical duplicates from conflicting duplicates
    fully_identical_mask = df.duplicated(keep=False)
    conflicting_rows = duplicate_rows[~duplicate_rows.index.isin(df[fully_identical_mask].index)]

    # Get the final deduplicated dataframe (keeping the first instance)
    deduplicated_df = df.drop_duplicates(subset=['material_id'], keep='first').copy()

    # --- Logging Duplicates and Warnings ---
    with open(log_file, 'w') as log:
        log.write("Duplicate Log\n")
        log.write("==============\n")
        duplicate_counts = df.groupby('material_id').size().reset_index(name='counts')
        duplicate_counts = duplicate_counts[duplicate_counts['counts'] > 1]
        for _, row in duplicate_counts.iterrows():
            log.write(f"material_id '{row['material_id']}' was repeated {row['counts']} times.\n")

    if not conflicting_rows.empty:
        print(f"Found {len(conflicting_rows)} conflicting rows. Saving to {warning_file}")
        conflicting_rows.to_csv(warning_file, index=False)
    else:
        print("No conflicting rows found.")

    # --- Update lowest_unit_price Step ---
    print("Updating 'lowest_unit_price' field...")
    
    # Ensure 'unit_price' is numeric, coercing errors
    deduplicated_df['unit_price'] = pd.to_numeric(deduplicated_df['unit_price'], errors='coerce')
    
    # Find the index of the minimum unit_price for each inchified_smiles group
    idx = deduplicated_df.groupby(['inchified_smiles'])['unit_price'].idxmin()
    
    # Initialize the column with False
    deduplicated_df['lowest_unit_price'] = False
    
    # Set to True for the rows with the lowest unit price
    deduplicated_df.loc[idx, 'lowest_unit_price'] = True

    # --- Update ID Column Step ---
    print("Updating ID column with sequential values...")
    
    # Reset index and update existing ID column with sequential values starting from 1
    deduplicated_df.reset_index(drop=True, inplace=True)
    deduplicated_df['id'] = range(1, len(deduplicated_df) + 1)

    # --- Save Final Output ---
    print(f"Saving processed data to {output_file}...")
    deduplicated_df.to_csv(output_file, index=False)

    print("\nProcessing finished!")
    print(f"- Deduplicated data saved to: {output_file}")
    print(f"- Duplicates log saved to: {log_file}")
    if not conflicting_rows.empty:
        print(f"- Conflicting rows saved to: {warning_file}")

def main():
    """Main function with standardized structure"""
    # Parse arguments
    parser = create_argument_parser()
    args = parser.parse_args()

    # Setup configuration
    config = ScriptConfig(
        batch_size=args.batch_size,
        timeout=args.timeout,
        log_level=args.log_level,
        log_dir=Path(args.log_dir) if args.log_dir else None,
        keep_duplicates=args.keep_duplicates,
        price_field=args.price_field,
        verbose=args.verbose,
        dry_run=args.dry_run
    )

    # Setup logging
    logger = setup_logging(config.log_level, config.log_dir, SCRIPT_NAME)

    try:
        # Get file paths (maintaining backward compatibility)
        input_file = args.input_file_path

        # Generate default file names if not provided
        input_dir, input_filename = os.path.split(input_file)
        input_name, input_ext = os.path.splitext(input_filename)

        output_file = args.output_file_path or os.path.join(input_dir, f"{input_name}_deduplicated{input_ext}")
        duplicates_log = args.duplicates_log_path or os.path.join(input_dir, f"{input_name}_duplicates.log")
        warnings_file = args.warnings_file_path or os.path.join(input_dir, f"{input_name}_warnings.csv")

        # Validate inputs
        if not os.path.exists(input_file):
            logger.error(f"Input file not found: {input_file}")
            sys.exit(1)

        if config.dry_run:
            logger.info("Dry run mode - validation only")
            logger.info(f"Would process: {input_file}")
            logger.info(f"Would output to: {output_file}")
            logger.info(f"Would save duplicates log to: {duplicates_log}")
            logger.info(f"Would save warnings to: {warnings_file}")
            logger.info(f"Keep duplicates: {config.keep_duplicates}")
            logger.info(f"Price field: {config.price_field}")
            return

        # Create processor and run
        processor = DeduplicationProcessor(config, logger)

        start_time = time.time()
        logger.info(f"Starting deduplication: {input_file}")

        # Use the process_csv method
        result = processor.process_csv(input_file, output_file, duplicates_log, warnings_file)

        execution_time = time.time() - start_time
        logger.info(f"Deduplication completed in {execution_time:.2f} seconds")

        # Report statistics
        stats = processor.get_statistics()
        logger.info(f"Statistics: {stats}")

        if not result.success:
            logger.error("Processing failed")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.warning("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        logger.debug(traceback.format_exc())
        sys.exit(1)

# Legacy function for backward compatibility
def main_legacy():
    """Legacy main function for backward compatibility"""
    parser = argparse.ArgumentParser(
        description="Deduplicate and update a CSV file based on material_id and unit prices.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("input_file", help="Path to the input CSV file.")
    parser.add_argument("output_file", nargs='?', default=None, help="Path for the output CSV file (optional). Defaults to <input>_processed.csv")
    parser.add_argument("log_file", nargs='?', default=None, help="Path for the duplicates log file (optional). Defaults to <input>_duplicates.log")
    parser.add_argument("warning_file", nargs='?', default=None, help="Path for the warnings CSV file (optional). Defaults to <input>_warnings.csv")

    args = parser.parse_args()

    input_dir, input_filename = os.path.split(args.input_file)
    input_name, input_ext = os.path.splitext(input_filename)

    output_file = args.output_file or os.path.join(input_dir, f"{input_name}_processed{input_ext}")
    log_file = args.log_file or os.path.join(input_dir, f"{input_name}_duplicates.log")
    warning_file = args.warning_file or os.path.join(input_dir, f"{input_name}_warnings.csv")

    process_csv_legacy(args.input_file, output_file, log_file, warning_file)

def process_dataframe(df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict], List[Dict]]:
    """处理DataFrame数据，进行去重和价格更新
    
    Args:
        df: 输入的DataFrame
        
    Returns:
        Tuple[pd.DataFrame, List[Dict], List[Dict]]: (处理后的DataFrame, 重复记录列表, 警告记录列表)
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始处理DataFrame，共 {len(df)} 行")
    
    # 检查必要字段
    required_fields = ['material_id', 'inchified_smiles', 'unit_price']
    missing_fields = [field for field in required_fields if field not in df.columns]
    if missing_fields:
        raise ValueError(f"DataFrame中缺少必要字段: {missing_fields}")
    
    duplicate_records = []
    warning_records = []
    
    # --- 去重步骤 ---
    logger.info("基于material_id进行去重...")
    
    # 找到所有基于material_id的重复项
    duplicates_mask = df.duplicated(subset=['material_id'], keep=False)
    duplicated_df = df[duplicates_mask]
    
    if not duplicated_df.empty:
        logger.info(f"发现 {len(duplicated_df)} 行重复数据")
        
        # 按material_id分组处理重复项
        for material_id, group in duplicated_df.groupby('material_id'):
            if len(group) > 1:
                # 检查是否为简单重复（所有字段都相同）
                if group.drop_duplicates().shape[0] == 1:
                    # 简单重复，记录重复信息
                    duplicate_records.extend([{
                        'material_id': material_id,
                        'duplicate_count': len(group),
                        'type': 'simple_duplicate',
                        'data': group.iloc[0].to_dict()
                    }])
                else:
                    # 冲突重复，记录警告
                    warning_records.extend([{
                        'material_id': material_id,
                        'conflict_count': len(group),
                        'type': 'conflicting_duplicate',
                        'conflicting_rows': group.to_dict('records')
                    }])
    
    # 保留每个material_id的第一次出现
    deduplicated_df = df.drop_duplicates(subset=['material_id'], keep='first')
    logger.info(f"去重后剩余 {len(deduplicated_df)} 行")
    
    # --- 价格更新步骤 ---
    logger.info("更新lowest_unit_price字段...")
    
    # 初始化lowest_unit_price字段
    deduplicated_df = deduplicated_df.copy()
    deduplicated_df['lowest_unit_price'] = False
    
    # 按inchified_smiles分组，找到每组中unit_price最低的行
    for inchi_smi, group in deduplicated_df.groupby('inchified_smiles'):
        if pd.isna(inchi_smi) or inchi_smi == '':
            continue
        
        # 找到unit_price最小的行的索引
        min_price_idx = group['unit_price'].idxmin()
        
        # 设置该行的lowest_unit_price为True
        deduplicated_df.loc[min_price_idx, 'lowest_unit_price'] = True
    
    # --- 更新ID列步骤 ---
    logger.info("更新ID列为顺序值...")
    
    # 重置索引并更新现有ID列为从1开始的顺序值
    deduplicated_df.reset_index(drop=True, inplace=True)
    deduplicated_df['id'] = range(1, len(deduplicated_df) + 1)
    
    logger.info(f"处理完成: 输出 {len(deduplicated_df)} 行, 重复记录 {len(duplicate_records)} 条, 警告 {len(warning_records)} 条")
    
    return deduplicated_df, duplicate_records, warning_records

def deduplicate_and_update_dataframe(df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
    """去重和更新DataFrame
    
    Args:
        df: 输入的DataFrame
        
    Returns:
        Tuple[pd.DataFrame, List[Dict]]: (处理后的DataFrame, 所有记录列表(包括重复和警告))
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始去重和更新DataFrame，共 {len(df)} 行")
    
    processed_df, duplicate_records, warning_records = process_dataframe(df)
    
    # 合并所有记录
    all_records = []
    all_records.extend([{**record, 'record_type': 'duplicate'} for record in duplicate_records])
    all_records.extend([{**record, 'record_type': 'warning'} for record in warning_records])
    
    return processed_df, all_records

if __name__ == "__main__":
    main()