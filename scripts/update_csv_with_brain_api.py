#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件更新脚本 - 使用Brain Server API

基于brain_server_client工具更新CSV文件中的canonical_smiles和inchified_smiles列
对于无效的SMILES，将行移动到error.csv文件中并添加错误原因

用法:
    python3 update_csv_with_brain_api.py input.csv
    python3 update_csv_with_brain_api.py input.csv output.csv error.csv
    python3 update_csv_with_brain_api.py input.csv --batch-size 5000 --verbose
"""

import sys
import os
import csv
import asyncio
import argparse
import pandas as pd
import logging
import logging.handlers
import time
import traceback
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass

# Script metadata
SCRIPT_NAME = "update_csv_with_brain_api"
VERSION = "1.0.0"
DEFAULT_BATCH_SIZE = 10000

# Custom exceptions
class ProcessingError(Exception):
    """Base exception for processing errors"""
    pass

class ValidationError(ProcessingError):
    """Exception for data validation errors"""
    pass

class APIError(ProcessingError):
    """Exception for API-related errors"""
    pass

@dataclass
class ProcessingResult:
    """Standardized result structure for processing operations"""
    success: bool
    total_processed: int
    success_count: int
    error_count: int
    warning_count: int
    error_details: List[Dict[str, Any]]
    execution_time: float

@dataclass
class ScriptConfig:
    """Configuration class for script parameters"""
    batch_size: int = DEFAULT_BATCH_SIZE
    timeout: int = 30
    log_level: str = 'INFO'
    log_dir: Optional[Path] = None
    cache_dir: Optional[Path] = None
    max_retries: int = 3
    retry_delay: float = 1.0
    verbose: bool = False
    dry_run: bool = False

def setup_logging(
    log_level: str = 'INFO',
    log_dir: Optional[Path] = None,
    script_name: str = SCRIPT_NAME
) -> logging.Logger:
    """Setup standardized logging configuration"""

    # Create logger
    logger = logging.getLogger(script_name)
    logger.setLevel(getattr(logging, log_level.upper()))

    # Clear existing handlers
    logger.handlers.clear()

    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )

    # Console handler (INFO and above)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)

    # File handlers (if log_dir specified)
    if log_dir:
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)

        # Main log file (all levels)
        main_log_file = log_dir / f'{script_name}.log'
        main_handler = logging.handlers.RotatingFileHandler(
            main_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        main_handler.setLevel(logging.DEBUG)
        main_handler.setFormatter(detailed_formatter)
        logger.addHandler(main_handler)

        # Error log file (WARNING and above)
        error_log_file = log_dir / f'{script_name}_errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        error_handler.setLevel(logging.WARNING)
        error_handler.setFormatter(detailed_formatter)
        logger.addHandler(error_handler)

    return logger

def create_argument_parser() -> argparse.ArgumentParser:
    """Create standardized argument parser"""
    parser = argparse.ArgumentParser(
        description="Update CSV file with Brain API SMILES data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""Examples:
  python3 {SCRIPT_NAME}.py input.csv
  python3 {SCRIPT_NAME}.py input.csv --batch-size 5000
  python3 {SCRIPT_NAME}.py input.csv output.csv error.csv --verbose"""
    )

    # Required positional arguments (maintaining backward compatibility)
    parser.add_argument('input_file_path', help='Input CSV file path')
    parser.add_argument('output_file_path', nargs='?',
                       help='Output CSV file path (optional, defaults to input_filename_updated.csv)')
    parser.add_argument('error_file_path', nargs='?',
                       help='Error CSV file path (optional, defaults to input_filename_errors.csv)')

    # Optional flag arguments (for backward compatibility)
    parser.add_argument('-o', '--output', dest='output_file_flag',
                       help='Specify output file (overrides positional argument)')
    parser.add_argument('-e', '--error', dest='error_file_flag',
                       help='Specify error file (overrides positional argument)')

    # Common optional arguments
    parser.add_argument('-b', '--batch-size', type=int, default=DEFAULT_BATCH_SIZE,
                       help=f'Batch processing size (default: {DEFAULT_BATCH_SIZE})')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Operation timeout in seconds (default: 30)')
    parser.add_argument('--cache-dir', default='./cache',
                       help='Cache directory path (default: ./cache)')
    parser.add_argument('--config-file', help='Configuration file path')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level (default: INFO)')
    parser.add_argument('--log-dir', help='Log directory path')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform validation without actual processing')
    parser.add_argument('--version', action='version', version=f'{SCRIPT_NAME} {VERSION}')

    return parser

# 添加brain_server_client模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'brain_server_client'))

try:
    from brain_server_client import BrainServerClient, SmilesResult
except ImportError:
    print("错误: 无法导入brain_server_client模块")
    print("请确保brain_server_client目录在正确位置")
    sys.exit(1)

from abc import ABC, abstractmethod

# Import cache manager
try:
    from optimized_cache_manager import OptimizedBatchCacheManager
except ImportError:
    print("Warning: Could not import OptimizedBatchCacheManager. Caching will be disabled.")
    OptimizedBatchCacheManager = None

class BaseUpdater(ABC):
    """Base class for all updater implementations"""

    def __init__(self, batch_size: int = DEFAULT_BATCH_SIZE, timeout: int = 30, **kwargs):
        self.batch_size = batch_size
        self.timeout = timeout
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.warning_count = 0

    @abstractmethod
    async def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """Update DataFrame and return success DataFrame and error records"""
        pass

    def update_csv(self, input_file: str, output_file: str, error_file: str) -> ProcessingResult:
        """Update CSV file and save results (synchronous wrapper)"""
        return asyncio.run(self._update_csv_async(input_file, output_file, error_file))

    async def _update_csv_async(self, input_file: str, output_file: str, error_file: str) -> ProcessingResult:
        """Async implementation of CSV update"""
        start_time = time.time()
        logger = logging.getLogger(__name__)

        try:
            # Load input data
            logger.info(f"Loading input file: {input_file}")
            df = pd.read_csv(input_file)
            logger.info(f"Loaded {len(df)} rows")

            # Process data
            success_df, error_records = await self.update_dataframe(df)

            # Save results
            if not success_df.empty:
                success_df.to_csv(output_file, index=False)
                logger.info(f"Saved {len(success_df)} successful rows to {output_file}")

            if error_records:
                error_df = pd.DataFrame(error_records)
                error_df.to_csv(error_file, index=False)
                logger.info(f"Saved {len(error_records)} error records to {error_file}")

            execution_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                total_processed=self.processed_count,
                success_count=self.success_count,
                error_count=self.error_count,
                warning_count=self.warning_count,
                error_details=error_records,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Processing failed: {str(e)}")
            return ProcessingResult(
                success=False,
                total_processed=self.processed_count,
                success_count=self.success_count,
                error_count=self.error_count,
                warning_count=self.warning_count,
                error_details=[],
                execution_time=execution_time
            )

    def get_statistics(self) -> Dict[str, Any]:
        """Return processing statistics"""
        return {
            'total_processed': self.processed_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'warning_count': self.warning_count,
            'success_rate': self.success_count / self.processed_count if self.processed_count > 0 else 0
        }

class CSVUpdater(BaseUpdater):
    def __init__(self, batch_size: int = 10000, timeout: int = 30, cache_dir: Optional[Path] = None, **kwargs):
        super().__init__(batch_size, timeout, **kwargs)
        self.client = BrainServerClient()

        # Initialize cache manager
        self.cache_manager = None
        self.api_calls_made = 0
        self.api_calls_saved = 0
        if OptimizedBatchCacheManager and cache_dir:
            try:
                self.cache_manager = OptimizedBatchCacheManager(
                    cache_dir=cache_dir,
                    memory_ttl=3600,  # 1 hour memory cache
                    file_ttl=86400,   # 24 hour file cache
                    max_memory_entries=50000
                )
                self.logger = logging.getLogger(__name__)
                self.logger.info(f"Cache manager initialized with directory: {cache_dir}")
            except Exception as e:
                self.logger = logging.getLogger(__name__)
                self.logger.warning(f"Failed to initialize cache manager: {e}")
                self.cache_manager = None

    def _generate_batch_key(self, smiles_list: List[str]) -> str:
        """Generate batch cache key based on sorted SMILES hash"""
        sorted_smiles = sorted(smiles_list)
        combined_string = "|".join(sorted_smiles)
        return hashlib.md5(combined_string.encode('utf-8')).hexdigest()

    async def process_batch(self, batch: List[Dict[str, Any]]) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        处理一批数据 (with caching support)
        返回: (成功更新的行, 错误行)
        """
        if not batch:
            return [], []

        rows_to_process = []
        error_rows = []
        success_rows = []

        # 分类处理行
        for row in batch:
            canonical_smiles = row.get('canonical_smiles', '').strip()
            if canonical_smiles == 'CC':
                row['canonical_smiles'] = 'CC'
                row['inchified_smiles'] = 'CC'
                success_rows.append(row)
            elif canonical_smiles:
                rows_to_process.append(row)
            else:
                row['error_reason'] = 'canonical_smiles字段为空'
                error_rows.append(row)

        if not rows_to_process:
            return success_rows, error_rows

        # 提取需要API处理的SMILES，并去重
        unique_smiles = list(set(r['canonical_smiles'] for r in rows_to_process))

        # Try to get cached results first
        cached_results = {}
        smiles_to_process = unique_smiles

        if self.cache_manager:
            batch_key = self._generate_batch_key(unique_smiles)
            cached_results, missing_smiles = self.cache_manager.get_cached_batch(
                batch_key, unique_smiles, cache_type='brain_api'
            )
            smiles_to_process = missing_smiles

            if cached_results:
                self.api_calls_saved += len(cached_results)
                self.logger.debug(f"Cache hit: {len(cached_results)}/{len(unique_smiles)} SMILES found in cache")
            if missing_smiles:
                self.logger.debug(f"Cache miss: {len(missing_smiles)} SMILES need API processing")

        # Process API calls for missing SMILES
        api_results = {}
        if smiles_to_process:
            try:
                # 调用API处理缺失的SMILES
                self.api_calls_made += len(smiles_to_process)
                results_list = await self.client.process_smiles(smiles_to_process)

                # 将结果映射回SMILES字符串
                api_results = {smiles: result for smiles, result in zip(smiles_to_process, results_list)}

                # Cache the new API results
                if self.cache_manager and api_results:
                    batch_key = self._generate_batch_key(smiles_to_process)
                    # Convert results to cacheable format
                    cache_data = {}
                    for smiles, result in api_results.items():
                        if result and result.canonical_smiles and result.inchified_smiles:
                            cache_data[smiles] = {
                                'canonical_smiles': result.canonical_smiles,
                                'inchified_smiles': result.inchified_smiles
                            }
                        else:
                            cache_data[smiles] = None

                    self.cache_manager.cache_batch_in_memory(
                        batch_key, smiles_to_process, cache_data, cache_type='brain_api'
                    )
                    self.logger.debug(f"Cached {len(cache_data)} new API results")

            except Exception as e:
                # API调用失败，所有待处理行都标记为错误
                for row in rows_to_process:
                    if row['canonical_smiles'] in smiles_to_process:
                        row['error_reason'] = f'API调用失败: {str(e)}'
                        error_rows.append(row)
                return success_rows, error_rows

        # Combine cached and API results
        all_results = {**cached_results, **api_results}

        # 处理所有待处理的行
        for row in rows_to_process:
            smiles = row['canonical_smiles']
            result = all_results.get(smiles)

            if result is None:
                # No result found (neither cached nor from API)
                row['error_reason'] = f'No result found for SMILES: {smiles}'
                error_rows.append(row)
            elif isinstance(result, dict):
                # Cached result format
                if result.get('canonical_smiles') and result.get('inchified_smiles'):
                    row['canonical_smiles'] = result['canonical_smiles']
                    row['inchified_smiles'] = result['inchified_smiles']
                    success_rows.append(row)
                else:
                    row['error_reason'] = f'Cached invalid SMILES: canonical={result.get("canonical_smiles")}, inchified={result.get("inchified_smiles")}'
                    error_rows.append(row)
            else:
                # API result format
                if not result or not result.canonical_smiles or not result.inchified_smiles:
                    # API返回None或空字符串表示无效SMILES
                    row['error_reason'] = f'API返回无效SMILES: canonical={result.canonical_smiles if result else None}, inchified={result.inchified_smiles if result else None}'
                    error_rows.append(row)
                else:
                    # 更新SMILES字段
                    row['canonical_smiles'] = result.canonical_smiles
                    row['inchified_smiles'] = result.inchified_smiles
                    success_rows.append(row)

        return success_rows, error_rows
    
    async def update_csv(self, input_file: str, output_file: str, error_file: str):
        """
        更新CSV文件 (maintaining backward compatibility)
        """
        logger = logging.getLogger(__name__)
        logger.info(f"Starting file processing: {input_file}")
        logger.info(f"Output file: {output_file}")
        logger.info(f"Error file: {error_file}")
        logger.info(f"Batch size: {self.batch_size}")

        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            logger.error(f"Input file not found: {input_file}")
            return

        # 读取CSV头部
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            fieldnames = reader.fieldnames

        if not fieldnames:
            logger.error("Unable to read CSV headers")
            return

        # 检查必要字段
        if 'canonical_smiles' not in fieldnames:
            logger.error("CSV file missing canonical_smiles field")
            return
            
        # 为错误文件添加error_reason字段
        error_fieldnames = fieldnames + ['error_reason']
        
        # 初始化输出文件
        with open(output_file, 'w', newline='', encoding='utf-8') as success_f, \
             open(error_file, 'w', newline='', encoding='utf-8') as error_f:
            
            success_writer = csv.DictWriter(success_f, fieldnames=fieldnames)
            error_writer = csv.DictWriter(error_f, fieldnames=error_fieldnames)
            
            success_writer.writeheader()
            error_writer.writeheader()
            
            # 流式处理CSV文件
            with open(input_file, 'r', encoding='utf-8') as input_f:
                reader = csv.DictReader(input_f)
                batch = []
                
                for row in reader:
                    batch.append(row)
                    
                    if len(batch) >= self.batch_size:
                        # 处理批次
                        success_rows, error_rows = await self.process_batch(batch)
                        
                        # 写入结果
                        for success_row in success_rows:
                            success_writer.writerow(success_row)
                            self.success_count += 1
                            
                        for error_row in error_rows:
                            error_writer.writerow(error_row)
                            self.error_count += 1
                            
                        self.processed_count += len(batch)
                        print(f"已处理: {self.processed_count} 行, 成功: {self.success_count}, 错误: {self.error_count}")
                        
                        batch = []
                        
                # 处理最后一批
                if batch:
                    success_rows, error_rows = await self.process_batch(batch)
                    
                    for success_row in success_rows:
                        success_writer.writerow(success_row)
                        self.success_count += 1
                        
                    for error_row in error_rows:
                        error_writer.writerow(error_row)
                        self.error_count += 1
                        
                    self.processed_count += len(batch)

        logger.info("Processing completed!")
        logger.info(f"Total processed: {self.processed_count}")
        logger.info(f"Successfully updated: {self.success_count}")
        logger.info(f"Error count: {self.error_count}")
        success_rate = self.success_count/self.processed_count*100 if self.processed_count > 0 else 0
        logger.info(f"Success rate: {success_rate:.2f}%")

        # Persist cache to disk
        if self.cache_manager:
            logger.info("Persisting cache to disk...")
            self.cache_manager.persist_cache_to_disk('brain_api')

            # Log cache statistics
            stats = self.cache_manager.get_stats()
            total_api_requests = self.api_calls_made + self.api_calls_saved
            api_reduction_rate = (self.api_calls_saved / total_api_requests * 100) if total_api_requests > 0 else 0

            logger.info(f"Cache statistics - Hit rate: {stats.hit_rate:.2%}, "
                       f"Hits: {stats.hits}, Partial hits: {stats.partial_hits}, "
                       f"Misses: {stats.misses}, Memory entries: {stats.memory_entries}")
            logger.info(f"API call reduction - Made: {self.api_calls_made}, "
                       f"Saved: {self.api_calls_saved}, Reduction: {api_reduction_rate:.1f}%")

        # 验证文件行数
        self._verify_row_counts(input_file, output_file, error_file)

    def _verify_row_counts(self, input_file: str, output_file: str, error_file: str):
        logger = logging.getLogger(__name__)
        try:
            # 获取行数，-1是为了去掉header
            with open(input_file, 'r', encoding='utf-8') as f:
                input_count = sum(1 for _ in f) - 1

            with open(output_file, 'r', encoding='utf-8') as f:
                output_count = sum(1 for _ in f) - 1

            with open(error_file, 'r', encoding='utf-8') as f:
                error_count = sum(1 for _ in f) - 1

            logger.info("--- File row count verification ---")
            logger.info(f"Original file rows: {input_count}")
            logger.info(f"Success file rows: {output_count}")
            logger.info(f"Error file rows: {error_count}")

            if input_count == output_count + error_count:
                logger.info("✅ Verification passed: Total row count matches")
            else:
                diff = input_count - (output_count + error_count)
                logger.warning(f"❌ Verification failed: Total row count mismatch! Difference: {diff}")

        except FileNotFoundError as e:
            logger.error(f"Row count verification failed: File not found {e.filename}")
        except Exception as e:
            logger.error(f"Unknown error during row count verification: {e}")
    
    async def process_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """处理DataFrame数据
        
        Args:
            df: 输入的DataFrame
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (成功数据DataFrame, 错误数据DataFrame)
        """
        logger = logging.getLogger(__name__)
        logger.info(f"开始处理DataFrame，共 {len(df)} 行")
        
        success_data = []
        error_data = []
        
        # 重置计数器
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
        # 分批处理
        for i in range(0, len(df), self.batch_size):
            batch_df = df.iloc[i:i + self.batch_size]
            batch = batch_df.to_dict('records')
            
            # 处理批次
            success_rows, error_rows = await self.process_batch(batch)
            
            # 收集结果
            success_data.extend(success_rows)
            error_data.extend(error_rows)
            
            self.processed_count += len(batch)
            self.success_count += len(success_rows)
            self.error_count += len(error_rows)
            
            logger.info(f"已处理: {self.processed_count} 行, 成功: {self.success_count}, 错误: {self.error_count}")
        
        # 创建结果DataFrame
        success_df = pd.DataFrame(success_data) if success_data else pd.DataFrame()
        error_df = pd.DataFrame(error_data) if error_data else pd.DataFrame()
        
        logger.info(f"处理完成: 总处理行数: {self.processed_count}, 成功: {self.success_count}, 错误: {self.error_count}")
        
        return success_df, error_df
    
    async def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """更新DataFrame中的SMILES数据
        
        Args:
            df: 输入的DataFrame，必须包含canonical_smiles列
            
        Returns:
            Tuple[pd.DataFrame, List[Dict]]: (成功更新的DataFrame, 错误记录列表)
        """
        logger = logging.getLogger(__name__)
        
        # 检查必要字段
        if 'canonical_smiles' not in df.columns:
            raise ValueError("DataFrame中缺少canonical_smiles字段")
        
        logger.info(f"开始更新DataFrame中的SMILES数据，共 {len(df)} 行")
        
        success_df, error_df = await self.process_dataframe(df)
        
        # 转换错误数据为记录列表
        error_records = error_df.to_dict('records') if not error_df.empty else []
        
        return success_df, error_records

async def main():
    """Main function with standardized structure"""
    # Parse arguments
    parser = create_argument_parser()
    args = parser.parse_args()

    # Setup configuration
    config = ScriptConfig(
        batch_size=args.batch_size,
        timeout=args.timeout,
        log_level=args.log_level,
        log_dir=Path(args.log_dir) if args.log_dir else None,
        cache_dir=Path(args.cache_dir) if args.cache_dir else None,
        verbose=args.verbose,
        dry_run=args.dry_run
    )

    # Setup logging
    logger = setup_logging(config.log_level, config.log_dir, SCRIPT_NAME)

    try:
        # Get file paths (maintaining backward compatibility)
        input_file = args.input_file_path

        # Generate default file names if not provided
        input_dir = os.path.dirname(input_file)
        input_name = os.path.splitext(os.path.basename(input_file))[0]
        input_ext = os.path.splitext(input_file)[1]

        # Determine output file (priority: flag > positional > default)
        if args.output_file_flag:
            output_file = args.output_file_flag
        elif args.output_file_path:
            output_file = args.output_file_path
        else:
            output_file = os.path.join(input_dir, f"{input_name}_updated{input_ext}")

        # Determine error file
        if args.error_file_flag:
            error_file = args.error_file_flag
        elif args.error_file_path:
            error_file = args.error_file_path
        else:
            error_file = os.path.join(input_dir, f"{input_name}_errors{input_ext}")

        # Validate inputs
        if not os.path.exists(input_file):
            logger.error(f"Input file not found: {input_file}")
            sys.exit(1)

        # Validate batch size
        if config.batch_size <= 0:
            logger.error("Batch size must be greater than 0")
            sys.exit(1)

        if config.batch_size > 50000:
            logger.warning("Large batch size may cause memory issues, recommended max: 50000")
            if not config.dry_run:
                response = input("Continue? (y/N): ")
                if response.lower() not in ['y', 'yes']:
                    sys.exit(0)

        if config.dry_run:
            logger.info("Dry run mode - validation only")
            logger.info(f"Would process: {input_file}")
            logger.info(f"Would output to: {output_file}")
            logger.info(f"Would save errors to: {error_file}")
            logger.info(f"Batch size: {config.batch_size}")
            return

        # Create updater and run
        updater = CSVUpdater(
            batch_size=config.batch_size,
            timeout=config.timeout,
            cache_dir=config.cache_dir
        )

        start_time = time.time()
        logger.info(f"Starting Brain API update: {input_file}")

        # Use the original update_csv method to maintain compatibility
        await updater.update_csv(input_file, output_file, error_file)

        execution_time = time.time() - start_time
        logger.info(f"Update completed in {execution_time:.2f} seconds")

        # Report statistics
        stats = updater.get_statistics()
        logger.info(f"Statistics: {stats}")

    except KeyboardInterrupt:
        logger.warning("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        logger.debug(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())