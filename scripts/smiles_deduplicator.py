#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SMILES去重系统

提供SMILES字符串去重功能，维护原始数据集位置映射，
支持结果重构和统计分析。

作者: Assistant
日期: 2024
"""

import logging
from typing import Dict, List, Set, Tuple, Any
from dataclasses import dataclass
from collections import defaultdict
import pandas as pd


@dataclass
class DeduplicationStats:
    """去重统计信息"""
    total_smiles: int = 0
    unique_smiles: int = 0
    duplicate_count: int = 0
    reduction_ratio: float = 0.0
    
    def calculate_reduction_ratio(self):
        """计算去重比例"""
        if self.total_smiles > 0:
            self.reduction_ratio = (self.total_smiles - self.unique_smiles) / self.total_smiles
        else:
            self.reduction_ratio = 0.0


class SmilesDeduplicator:
    """SMILES去重器"""
    
    def __init__(self, smiles_column: str = 'canonical_smiles'):
        self.smiles_column = smiles_column
        self.logger = logging.getLogger(__name__)
        
        # 去重映射：unique_smiles -> [original_indices]
        self.smiles_to_indices: Dict[str, List[int]] = defaultdict(list)
        
        # 唯一SMILES列表
        self.unique_smiles: List[str] = []
        
        # 原始数据索引到唯一SMILES的映射
        self.index_to_unique_smiles: Dict[int, str] = {}
        
        # 统计信息
        self.stats = DeduplicationStats()
    
    def deduplicate_dataframe(self, df: pd.DataFrame) -> Tuple[List[str], Dict[str, List[int]]]:
        """
        对DataFrame中的SMILES进行去重
        
        Args:
            df: 包含SMILES数据的DataFrame
            
        Returns:
            Tuple[List[str], Dict[str, List[int]]]: (唯一SMILES列表, SMILES到原始索引的映射)
        """
        if self.smiles_column not in df.columns:
            raise ValueError(f"DataFrame中缺少{self.smiles_column}列")
        
        self.logger.info(f"开始对{len(df)}行数据进行SMILES去重")
        
        # 重置状态
        self.smiles_to_indices.clear()
        self.unique_smiles.clear()
        self.index_to_unique_smiles.clear()
        
        # 统计原始数据
        self.stats.total_smiles = len(df)
        
        # 遍历DataFrame，建立映射关系
        for idx, row in df.iterrows():
            smiles = str(row[self.smiles_column]).strip()
            
            # 跳过空值
            if not smiles or smiles.lower() in ['nan', 'none', '']:
                continue
            
            # 记录映射关系
            if smiles not in self.smiles_to_indices:
                self.unique_smiles.append(smiles)
            
            self.smiles_to_indices[smiles].append(idx)
            self.index_to_unique_smiles[idx] = smiles
        
        # 更新统计信息
        self.stats.unique_smiles = len(self.unique_smiles)
        self.stats.duplicate_count = self.stats.total_smiles - self.stats.unique_smiles
        self.stats.calculate_reduction_ratio()
        
        self.logger.info(f"去重完成: 原始{self.stats.total_smiles}个 -> 唯一{self.stats.unique_smiles}个")
        self.logger.info(f"去重率: {self.stats.reduction_ratio:.2%}")
        
        return self.unique_smiles, dict(self.smiles_to_indices)
    
    def deduplicate_list(self, smiles_list: List[str]) -> Tuple[List[str], Dict[str, List[int]]]:
        """
        对SMILES列表进行去重
        
        Args:
            smiles_list: SMILES字符串列表
            
        Returns:
            Tuple[List[str], Dict[str, List[int]]]: (唯一SMILES列表, SMILES到原始索引的映射)
        """
        self.logger.info(f"开始对{len(smiles_list)}个SMILES进行去重")
        
        # 重置状态
        self.smiles_to_indices.clear()
        self.unique_smiles.clear()
        self.index_to_unique_smiles.clear()
        
        # 统计原始数据
        self.stats.total_smiles = len(smiles_list)
        
        # 遍历列表，建立映射关系
        for idx, smiles in enumerate(smiles_list):
            smiles = str(smiles).strip()
            
            # 跳过空值
            if not smiles or smiles.lower() in ['nan', 'none', '']:
                continue
            
            # 记录映射关系
            if smiles not in self.smiles_to_indices:
                self.unique_smiles.append(smiles)
            
            self.smiles_to_indices[smiles].append(idx)
            self.index_to_unique_smiles[idx] = smiles
        
        # 更新统计信息
        self.stats.unique_smiles = len(self.unique_smiles)
        self.stats.duplicate_count = self.stats.total_smiles - self.stats.unique_smiles
        self.stats.calculate_reduction_ratio()
        
        self.logger.info(f"去重完成: 原始{self.stats.total_smiles}个 -> 唯一{self.stats.unique_smiles}个")
        self.logger.info(f"去重率: {self.stats.reduction_ratio:.2%}")
        
        return self.unique_smiles, dict(self.smiles_to_indices)
    
    def reconstruct_results(
        self, 
        unique_results: Dict[str, Any], 
        original_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        根据唯一SMILES的处理结果重构原始DataFrame
        
        Args:
            unique_results: 唯一SMILES的处理结果 {smiles: result_data}
            original_df: 原始DataFrame
            
        Returns:
            pd.DataFrame: 重构后的DataFrame
        """
        if not self.smiles_to_indices:
            raise ValueError("请先调用deduplicate_dataframe或deduplicate_list方法")
        
        self.logger.info("开始重构处理结果")
        
        # 创建结果DataFrame的副本
        result_df = original_df.copy()
        
        # 遍历每个唯一SMILES的结果
        for smiles, result_data in unique_results.items():
            if smiles in self.smiles_to_indices:
                # 获取所有使用这个SMILES的原始索引
                original_indices = self.smiles_to_indices[smiles]
                
                # 将结果应用到所有相关的原始行
                for idx in original_indices:
                    if idx in result_df.index:
                        # 更新结果数据
                        if isinstance(result_data, dict):
                            for column, value in result_data.items():
                                result_df.at[idx, column] = value
                        else:
                            # 如果result_data不是字典，假设它是要更新的单个值
                            result_df.at[idx, self.smiles_column] = result_data
        
        self.logger.info("结果重构完成")
        return result_df
    
    def get_unique_smiles_batch(self, batch_size: int = 10000) -> List[List[str]]:
        """
        将唯一SMILES分批返回
        
        Args:
            batch_size: 批次大小
            
        Returns:
            List[List[str]]: SMILES批次列表
        """
        if not self.unique_smiles:
            return []
        
        batches = []
        for i in range(0, len(self.unique_smiles), batch_size):
            batch = self.unique_smiles[i:i + batch_size]
            batches.append(batch)
        
        self.logger.info(f"将{len(self.unique_smiles)}个唯一SMILES分为{len(batches)}个批次")
        return batches
    
    def get_stats(self) -> DeduplicationStats:
        """获取去重统计信息"""
        return self.stats
    
    def get_duplicate_analysis(self) -> Dict[str, Any]:
        """获取重复SMILES分析"""
        duplicate_analysis = {
            'total_duplicates': 0,
            'max_duplicates': 0,
            'duplicate_distribution': defaultdict(int),
            'most_duplicated_smiles': []
        }
        
        duplicate_counts = []
        for smiles, indices in self.smiles_to_indices.items():
            count = len(indices)
            if count > 1:
                duplicate_analysis['total_duplicates'] += count - 1
                duplicate_counts.append((smiles, count))
                duplicate_analysis['duplicate_distribution'][count] += 1
        
        if duplicate_counts:
            duplicate_analysis['max_duplicates'] = max(count for _, count in duplicate_counts)
            # 获取重复次数最多的前10个SMILES
            duplicate_counts.sort(key=lambda x: x[1], reverse=True)
            duplicate_analysis['most_duplicated_smiles'] = duplicate_counts[:10]
        
        return duplicate_analysis
    
    def print_stats(self):
        """打印去重统计信息"""
        print("\n" + "=" * 60)
        print("SMILES去重统计信息")
        print("=" * 60)
        print(f"原始SMILES数量: {self.stats.total_smiles:,}")
        print(f"唯一SMILES数量: {self.stats.unique_smiles:,}")
        print(f"重复SMILES数量: {self.stats.duplicate_count:,}")
        print(f"去重率: {self.stats.reduction_ratio:.2%}")
        
        # 重复分析
        analysis = self.get_duplicate_analysis()
        if analysis['total_duplicates'] > 0:
            print(f"\n重复分析:")
            print(f"总重复次数: {analysis['total_duplicates']:,}")
            print(f"最大重复次数: {analysis['max_duplicates']:,}")
            
            print(f"\n重复次数分布:")
            for count, freq in sorted(analysis['duplicate_distribution'].items()):
                print(f"  重复{count}次: {freq:,}个SMILES")
            
            if analysis['most_duplicated_smiles']:
                print(f"\n重复最多的SMILES (前5个):")
                for smiles, count in analysis['most_duplicated_smiles'][:5]:
                    print(f"  {smiles}: {count}次")
        
        print("=" * 60)
