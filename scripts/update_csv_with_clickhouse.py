#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV file update script - using ClickHouse

Updates the CSV file by filling in the codes and pubchem_safety_link columns based on the inchified_smiles column.
For rows where inchified_smiles is not found in the ClickHouse database, move the row to an error.csv file and add the reason for the error.

Usage:
    python3 update_csv_with_clickhouse.py input.csv
    python3 update_csv_with_clickhouse.py input.csv output.csv error.csv
    python3 update_csv_with_clickhouse.py input.csv --clickhouse-host 127.0.0.1 --verbose
"""

import os
import csv
import argparse
import pandas as pd
import logging
import logging.handlers
import time
import traceback
import sys
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from dataclasses import dataclass
from abc import ABC, abstractmethod
from clickhouse_driver import Client

# Script metadata
SCRIPT_NAME = "update_csv_with_clickhouse"
VERSION = "1.0.0"
DEFAULT_BATCH_SIZE = 5000
DEFAULT_CLICKHOUSE_HOST = "ec2-161-189-187-79.cn-northwest-1.compute.amazonaws.com.cn"

# Custom exceptions
class ProcessingError(Exception):
    """Base exception for processing errors"""
    pass

class ValidationError(ProcessingError):
    """Exception for data validation errors"""
    pass

class DatabaseError(ProcessingError):
    """Exception for database-related errors"""
    pass

@dataclass
class ProcessingResult:
    """Standardized result structure for processing operations"""
    success: bool
    total_processed: int
    success_count: int
    error_count: int
    warning_count: int
    error_details: List[Dict[str, Any]]
    execution_time: float

@dataclass
class ScriptConfig:
    """Configuration class for script parameters"""
    batch_size: int = DEFAULT_BATCH_SIZE
    timeout: int = 30
    log_level: str = 'INFO'
    log_dir: Optional[Path] = None
    clickhouse_host: str = DEFAULT_CLICKHOUSE_HOST
    clickhouse_port: int = 9000
    verbose: bool = False
    dry_run: bool = False

def setup_logging(
    log_level: str = 'INFO',
    log_dir: Optional[Path] = None,
    script_name: str = SCRIPT_NAME
) -> logging.Logger:
    """Setup standardized logging configuration"""

    # Create logger
    logger = logging.getLogger(script_name)
    logger.setLevel(getattr(logging, log_level.upper()))

    # Clear existing handlers
    logger.handlers.clear()

    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )

    # Console handler (INFO and above)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)

    # File handlers (if log_dir specified)
    if log_dir:
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)

        # Main log file (all levels)
        main_log_file = log_dir / f'{script_name}.log'
        main_handler = logging.handlers.RotatingFileHandler(
            main_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        main_handler.setLevel(logging.DEBUG)
        main_handler.setFormatter(detailed_formatter)
        logger.addHandler(main_handler)

        # Error log file (WARNING and above)
        error_log_file = log_dir / f'{script_name}_errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        error_handler.setLevel(logging.WARNING)
        error_handler.setFormatter(detailed_formatter)
        logger.addHandler(error_handler)

    return logger

def create_argument_parser() -> argparse.ArgumentParser:
    """Create standardized argument parser"""
    parser = argparse.ArgumentParser(
        description="Update CSV file with ClickHouse hazard data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""Examples:
  python3 {SCRIPT_NAME}.py input.csv
  python3 {SCRIPT_NAME}.py input.csv --clickhouse-host 127.0.0.1
  python3 {SCRIPT_NAME}.py input.csv output.csv error.csv --verbose"""
    )

    # Required positional arguments (maintaining backward compatibility)
    parser.add_argument('input_file_path', help='Input CSV file path')
    parser.add_argument('output_file_path', nargs='?',
                       help='Output CSV file path (optional, defaults to input_filename_updated.csv)')
    parser.add_argument('error_file_path', nargs='?',
                       help='Error CSV file path (optional, defaults to input_filename_errors.csv)')

    # Optional flag arguments (for backward compatibility)
    parser.add_argument('-o', '--output', dest='output_file_flag',
                       help='Specify output file (overrides positional argument)')
    parser.add_argument('-e', '--error', dest='error_file_flag',
                       help='Specify error file (overrides positional argument)')

    # ClickHouse specific arguments
    parser.add_argument('--clickhouse-host', default=DEFAULT_CLICKHOUSE_HOST,
                       help=f'ClickHouse host (default: {DEFAULT_CLICKHOUSE_HOST})')
    parser.add_argument('--clickhouse-port', type=int, default=9000,
                       help='ClickHouse port (default: 9000)')

    # Common optional arguments
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE,
                       help=f'Batch processing size (default: {DEFAULT_BATCH_SIZE})')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Operation timeout in seconds (default: 30)')
    parser.add_argument('--config-file', help='Configuration file path')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level (default: INFO)')
    parser.add_argument('--log-dir', help='Log directory path')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform validation without actual processing')
    parser.add_argument('--version', action='version', version=f'{SCRIPT_NAME} {VERSION}')

    return parser

class BaseUpdater(ABC):
    """Base class for all updater implementations"""

    def __init__(self, batch_size: int = DEFAULT_BATCH_SIZE, timeout: int = 30, **kwargs):
        self.batch_size = batch_size
        self.timeout = timeout
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.warning_count = 0

    @abstractmethod
    def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """Update DataFrame and return success DataFrame and error records"""
        pass

    def update_csv(self, input_file: str, output_file: str, error_file: str) -> ProcessingResult:
        """Update CSV file and save results"""
        start_time = time.time()
        logger = logging.getLogger(__name__)

        try:
            # Load input data
            logger.info(f"Loading input file: {input_file}")
            df = pd.read_csv(input_file)
            logger.info(f"Loaded {len(df)} rows")

            # Process data
            success_df, error_records = self.update_dataframe(df)

            # Save results
            if not success_df.empty:
                success_df.to_csv(output_file, index=False)
                logger.info(f"Saved {len(success_df)} successful rows to {output_file}")

            if error_records:
                error_df = pd.DataFrame(error_records)
                error_df.to_csv(error_file, index=False)
                logger.info(f"Saved {len(error_records)} error records to {error_file}")

            execution_time = time.time() - start_time

            return ProcessingResult(
                success=True,
                total_processed=self.processed_count,
                success_count=self.success_count,
                error_count=self.error_count,
                warning_count=self.warning_count,
                error_details=error_records,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Processing failed: {str(e)}")
            return ProcessingResult(
                success=False,
                total_processed=self.processed_count,
                success_count=self.success_count,
                error_count=self.error_count,
                warning_count=self.warning_count,
                error_details=[],
                execution_time=execution_time
            )

    def get_statistics(self) -> Dict[str, Any]:
        """Return processing statistics"""
        return {
            'total_processed': self.processed_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'warning_count': self.warning_count,
            'success_rate': self.success_count / self.processed_count if self.processed_count > 0 else 0
        }


class CSVUpdater(BaseUpdater):
    def __init__(self, clickhouse_host: str = DEFAULT_CLICKHOUSE_HOST, clickhouse_port: int = 9000,
                 batch_size: int = DEFAULT_BATCH_SIZE, timeout: int = 30, **kwargs):
        super().__init__(batch_size, timeout, **kwargs)
        self.client = Client(host=clickhouse_host, port=clickhouse_port)

    def query_hazards_batch(
        self, inchi_smis: list[str]
    ) -> Dict[str, Dict[str, Any]]:
        """
        Query hazard information from ClickHouse for a batch of inchified_smiles.
        """
        if not inchi_smis:
            return {}

        # To avoid "Max query size exceeded", we split the query into smaller chunks.
        chunk_size = 1000
        all_results = {}
        for i in range(0, len(inchi_smis), chunk_size):
            chunk = inchi_smis[i:i + chunk_size]
            query = f"""
            SELECT inchi_smi, cid, hazards
            FROM default.pubchem_hazards
            WHERE inchi_smi IN {tuple(chunk)}
            """
            result = self.client.execute(query)
            for row in result:
                all_results[row[0]] = {"cid": row[1], "hazards": row[2]}
        return all_results

    def update_csv(
        self, input_file: str, output_file: str, error_file: str, batch_size: int = 10000
    ) -> ProcessingResult:
        """
        Update the CSV file.
        """
        start_time = time.time()
        print(f"Starting to process file: {input_file}")
        print(f"Output file: {output_file}")
        print(f"Error file: {error_file}")

        if not os.path.exists(input_file):
            error_msg = f"Input file not found: {input_file}"
            print(f"Error: {error_msg}")
            return ProcessingResult(
                success=False,
                total_processed=0,
                success_count=0,
                error_count=0,
                warning_count=0,
                error_details=[{"error": error_msg}],
                execution_time=time.time() - start_time
            )

        with open(input_file, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            fieldnames = reader.fieldnames

        if not fieldnames:
            error_msg = "Could not read CSV header"
            print(f"Error: {error_msg}")
            return ProcessingResult(
                success=False,
                total_processed=0,
                success_count=0,
                error_count=0,
                warning_count=0,
                error_details=[{"error": error_msg}],
                execution_time=time.time() - start_time
            )

        if "inchified_smiles" not in fieldnames:
            error_msg = "'inchified_smiles' field is missing from the CSV file"
            print(f"Error: {error_msg}")
            return ProcessingResult(
                success=False,
                total_processed=0,
                success_count=0,
                error_count=0,
                warning_count=0,
                error_details=[{"error": error_msg}],
                execution_time=time.time() - start_time
            )

        output_fieldnames = list(fieldnames)
        if "codes" not in output_fieldnames:
            output_fieldnames.append("codes")
        if "pubchem_safety_link" not in output_fieldnames:
            output_fieldnames.append("pubchem_safety_link")

        error_fieldnames = list(fieldnames)
        if "error_reason" not in error_fieldnames:
            error_fieldnames.append("error_reason")

        with (
            open(output_file, "w", newline="", encoding="utf-8") as success_f,
            open(error_file, "w", newline="", encoding="utf-8") as error_f,
        ):
            success_writer = csv.DictWriter(success_f, fieldnames=output_fieldnames)
            error_writer = csv.DictWriter(error_f, fieldnames=error_fieldnames)

            success_writer.writeheader()
            error_writer.writeheader()

            with open(input_file, "r", encoding="utf-8") as input_f:
                reader = csv.DictReader(input_f)
                batch = []
                for row in reader:
                    batch.append(row)
                    if len(batch) >= batch_size:
                        self._process_batch(
                            batch, success_writer, error_writer
                        )
                        batch = []

                if batch:
                    self._process_batch(batch, success_writer, error_writer)

        self._print_summary()
        self._verify_row_counts(input_file, output_file, error_file)
        
        # Return processing result
        execution_time = time.time() - start_time
        return ProcessingResult(
            success=True,
            total_processed=self.processed_count,
            success_count=self.success_count,
            error_count=self.error_count,
            warning_count=self.warning_count,
            error_details=[],
            execution_time=execution_time
        )

    def _process_batch(self, batch, success_writer, error_writer):
        self.processed_count += len(batch)
        unique_smiles = list(
            set(
                row.get("inchified_smiles", "").strip()
                for row in batch
                if row.get("inchified_smiles", "").strip()
            )
        )

        hazard_data = self.query_hazards_batch(unique_smiles)

        for row in batch:
            inchi_smi = row.get("inchified_smiles", "").strip()

            if not inchi_smi:
                row["error_reason"] = "inchified_smiles field is empty"
                error_writer.writerow(row)
                self.error_count += 1
                continue

            hazard_info = hazard_data.get(inchi_smi)

            if hazard_info:
                cid = hazard_info["cid"]
                hazards = hazard_info["hazards"]
                row["codes"] = hazards.split("|")
                row["pubchem_safety_link"] = (
                    f"https://pubchem.ncbi.nlm.nih.gov/compound/{cid}#section=Safety-and-Hazards"
                )
                success_writer.writerow(row)
                self.success_count += 1
            else:
                row["codes"] = []
                row["pubchem_safety_link"] = ""
                success_writer.writerow(row)
                self.warning_count += 1

        print(
            f"Processed: {self.processed_count} rows, Success: {self.success_count}, Error: {self.error_count}, Warning: {self.warning_count}"
        )

    def _print_summary(self):
        print(f"\nProcessing finished!")
        print(f"Total rows processed: {self.processed_count}")
        print(f"Successfully updated: {self.success_count}")
        print(f"Rows with warnings (not found in ClickHouse): {self.warning_count}")
        print(f"Error rows: {self.error_count}")
        if self.processed_count > 0:
            success_rate = self.success_count / self.processed_count * 100
            warning_rate = self.warning_count / self.processed_count * 100
            error_rate = self.error_count / self.processed_count * 100
            print(f"Success rate: {success_rate:.2f}%")
            print(f"Warning rate: {warning_rate:.2f}%")
            print(f"Error rate: {error_rate:.2f}%")
        else:
            print("Success rate: 0%")

    def _verify_row_counts(self, input_file: str, output_file: str, error_file: str):
        try:
            # Get line count, -1 for header
            with open(input_file, 'r', encoding='utf-8') as f:
                input_count = sum(1 for _ in f) - 1
            
            with open(output_file, 'r', encoding='utf-8') as f:
                # The output file contains successful and warning rows
                output_count = sum(1 for _ in f) - 1

            with open(error_file, 'r', encoding='utf-8') as f:
                error_count = sum(1 for _ in f) - 1

            print("\n--- File Row Count Verification ---")
            print(f"Original file rows: {input_count}")
            print(f"Output file rows (Success + Warnings): {output_count}")
            print(f"Error file rows: {error_count}")

            if input_count == output_count + error_count:
                print("✅ Verification Passed: Total row count matches.")
            else:
                print(f"❌ Verification Failed: Total row count mismatch! Difference: {input_count - (output_count + error_count)}")

        except FileNotFoundError as e:
            print(f"Row count verification failed: File not found {e.filename}")
        except Exception as e:
            print(f"An unknown error occurred during row count verification: {e}")
    
    def process_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """处理DataFrame数据
        
        Args:
            df: 输入的DataFrame，必须包含inchified_smiles列
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (成功数据DataFrame, 错误数据DataFrame)
        """
        logger = logging.getLogger(__name__)
        
        # 检查必要字段
        if 'inchified_smiles' not in df.columns:
            raise ValueError("DataFrame中缺少inchified_smiles字段")
        
        logger.info(f"开始处理DataFrame，共 {len(df)} 行")
        
        # 重置计数器
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.warning_count = 0
        
        success_data = []
        error_data = []
        
        # 获取所有唯一的inchified_smiles
        unique_inchi_smis = df['inchified_smiles'].dropna().unique().tolist()
        
        if unique_inchi_smis:
            # 批量查询hazard信息
            hazards_dict = self.query_hazards_batch(unique_inchi_smis)
            logger.info(f"从ClickHouse查询到 {len(hazards_dict)} 条hazard信息")
        else:
            hazards_dict = {}
        
        # 处理每一行
        for idx, row in df.iterrows():
            row_dict = row.to_dict()
            inchi_smi = row_dict.get('inchified_smiles', '').strip()
            
            self.processed_count += 1
            
            if not inchi_smi:
                row_dict['error_reason'] = 'inchified_smiles字段为空'
                error_data.append(row_dict)
                self.error_count += 1
            elif inchi_smi in hazards_dict:
                # 找到匹配的hazard信息
                hazard_info = hazards_dict[inchi_smi]
                hazards = hazard_info['hazards']
                row_dict['codes'] = hazards.split('|') if hazards else []
                row_dict['pubchem_safety_link'] = f"https://pubchem.ncbi.nlm.nih.gov/compound/{hazard_info['cid']}#section=Safety-and-Hazards"
                success_data.append(row_dict)
                self.success_count += 1
            else:
                # 未找到匹配的hazard信息，作为警告处理
                row_dict['codes'] = []
                row_dict['pubchem_safety_link'] = ''
                success_data.append(row_dict)
                self.warning_count += 1
            
            # 进度报告
            if self.processed_count % 10000 == 0:
                logger.info(f"已处理: {self.processed_count} 行, 成功: {self.success_count}, 错误: {self.error_count}")
        
        # 创建结果DataFrame
        success_df = pd.DataFrame(success_data) if success_data else pd.DataFrame()
        error_df = pd.DataFrame(error_data) if error_data else pd.DataFrame()
        
        logger.info(f"处理完成: 总处理行数: {self.processed_count}, 成功: {self.success_count}, 错误: {self.error_count}")
        
        return success_df, error_df
    
    def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """更新DataFrame中的hazard信息
        
        Args:
            df: 输入的DataFrame，必须包含inchified_smiles列
            
        Returns:
            Tuple[pd.DataFrame, List[Dict]]: (成功更新的DataFrame, 错误记录列表)
        """
        logger = logging.getLogger(__name__)
        logger.info(f"开始更新DataFrame中的hazard信息，共 {len(df)} 行")
        
        success_df, error_df = self.process_dataframe(df)
        
        # 转换错误数据为记录列表
        error_records = error_df.to_dict('records') if not error_df.empty else []
        
        return success_df, error_records


def main():
    """Main function with standardized structure"""
    # Parse arguments
    parser = create_argument_parser()
    args = parser.parse_args()

    # Setup configuration
    config = ScriptConfig(
        batch_size=args.batch_size,
        timeout=args.timeout,
        log_level=args.log_level,
        log_dir=Path(args.log_dir) if args.log_dir else None,
        clickhouse_host=args.clickhouse_host,
        clickhouse_port=args.clickhouse_port,
        verbose=args.verbose,
        dry_run=args.dry_run
    )

    # Setup logging
    logger = setup_logging(config.log_level, config.log_dir, SCRIPT_NAME)

    try:
        # Get file paths (maintaining backward compatibility)
        input_file = args.input_file_path

        # Generate default file names if not provided
        input_dir = os.path.dirname(input_file)
        input_name, input_ext = os.path.splitext(os.path.basename(input_file))

        # Determine output file (priority: flag > positional > default)
        if args.output_file_flag:
            output_file = args.output_file_flag
        elif args.output_file_path:
            output_file = args.output_file_path
        else:
            output_file = os.path.join(input_dir, f"{input_name}_updated{input_ext}")

        # Determine error file
        if args.error_file_flag:
            error_file = args.error_file_flag
        elif args.error_file_path:
            error_file = args.error_file_path
        else:
            error_file = os.path.join(input_dir, f"{input_name}_errors{input_ext}")

        # Validate inputs
        if not os.path.exists(input_file):
            logger.error(f"Input file not found: {input_file}")
            sys.exit(1)

        if config.dry_run:
            logger.info("Dry run mode - validation only")
            logger.info(f"Would process: {input_file}")
            logger.info(f"Would output to: {output_file}")
            logger.info(f"Would save errors to: {error_file}")
            logger.info(f"ClickHouse host: {config.clickhouse_host}:{config.clickhouse_port}")
            logger.info(f"Batch size: {config.batch_size}")
            return

        # Create updater and run
        updater = CSVUpdater(
            clickhouse_host=config.clickhouse_host,
            clickhouse_port=config.clickhouse_port,
            batch_size=config.batch_size,
            timeout=config.timeout
        )

        start_time = time.time()
        logger.info(f"Starting ClickHouse update: {input_file}")

        # Use the update_csv method
        result = updater.update_csv(input_file, output_file, error_file)

        execution_time = time.time() - start_time
        logger.info(f"Update completed in {execution_time:.2f} seconds")

        # Report statistics
        stats = updater.get_statistics()
        logger.info(f"Statistics: {stats}")

        # Check if result is None (defensive programming)
        if result is None:
            logger.error("Processing failed: No result returned")
            sys.exit(1)
            
        if not result.success:
            logger.error("Processing failed")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.warning("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        logger.debug(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
