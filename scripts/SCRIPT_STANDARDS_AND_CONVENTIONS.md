# Script Standards and Conventions

## Overview

This document defines the standardized interfaces, argument naming conventions, logging patterns, error handling, and coding standards for all pipeline scripts in the material-data processing system.

## 1. Command-Line Interface Standards

### 1.1 Argument Naming Conventions

All scripts must use consistent parameter names for common inputs:

**Required Positional Arguments:**
- `input_file_path` - Input file path (first positional argument)
- `output_file_path` - Output file path (second positional argument, optional with default)
- `error_file_path` - Error file path (third positional argument, optional with default)

**Optional Named Arguments:**
- `--batch-size` - Batch processing size (default varies by script)
- `--timeout` - Operation timeout in seconds
- `--config-file` - Configuration file path
- `--log-level` - Logging level (DEBUG, INFO, WARNING, ERROR)
- `--log-dir` - Log directory path
- `--verbose` - Enable verbose output
- `--dry-run` - Perform validation without actual processing
- `--version` - Display version information

**Script-Specific Arguments:**
- Brain API: `--brain-api-timeout`, `--max-retries`
- ClickHouse: `--clickhouse-host`, `--clickhouse-port`, `--clickhouse-timeout`
- Deduplication: `--keep-duplicates`, `--price-field`

### 1.2 Argument Parser Configuration

```python
def create_argument_parser() -> argparse.ArgumentParser:
    """Create standardized argument parser"""
    parser = argparse.ArgumentParser(
        description="Script description",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""Examples:
  python3 script.py input.csv
  python3 script.py input.csv --batch-size 5000
  python3 script.py input.csv output.csv error.csv --verbose"""
    )
    
    # Required positional arguments
    parser.add_argument('input_file_path', help='Input file path')
    parser.add_argument('output_file_path', nargs='?', help='Output file path (optional)')
    parser.add_argument('error_file_path', nargs='?', help='Error file path (optional)')
    
    # Common optional arguments
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE,
                       help=f'Batch processing size (default: {DEFAULT_BATCH_SIZE})')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Operation timeout in seconds (default: 30)')
    parser.add_argument('--config-file', help='Configuration file path')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level (default: INFO)')
    parser.add_argument('--log-dir', help='Log directory path')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform validation without actual processing')
    parser.add_argument('--version', action='version', version=f'{SCRIPT_NAME} {VERSION}')
    
    return parser
```

## 2. Logging System Standards

### 2.1 Logging Configuration

All scripts must implement unified logging with the following structure:

```python
import logging
import logging.handlers
from pathlib import Path
from typing import Optional

def setup_logging(
    log_level: str = 'INFO',
    log_dir: Optional[Path] = None,
    script_name: str = 'pipeline_script'
) -> logging.Logger:
    """Setup standardized logging configuration"""
    
    # Create logger
    logger = logging.getLogger(script_name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Console handler (INFO and above)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)
    
    # File handlers (if log_dir specified)
    if log_dir:
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Main log file (all levels)
        main_log_file = log_dir / f'{script_name}.log'
        main_handler = logging.handlers.RotatingFileHandler(
            main_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        main_handler.setLevel(logging.DEBUG)
        main_handler.setFormatter(detailed_formatter)
        logger.addHandler(main_handler)
        
        # Error log file (WARNING and above)
        error_log_file = log_dir / f'{script_name}_errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        error_handler.setLevel(logging.WARNING)
        error_handler.setFormatter(detailed_formatter)
        logger.addHandler(error_handler)
    
    return logger
```

### 2.2 Logging Patterns

**Progress Logging:**
```python
# Use INFO level for progress information
logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} items)")
logger.info(f"Completed: {completed_count}/{total_count} ({percentage:.1f}%)")
```

**Error Logging:**
```python
# Use ERROR level for critical errors
logger.error(f"Failed to process item {item_id}: {error_message}")

# Use WARNING level for recoverable issues
logger.warning(f"Skipping invalid item {item_id}: {reason}")
```

**Debug Logging:**
```python
# Use DEBUG level for detailed debugging information
logger.debug(f"Processing item: {item_data}")
logger.debug(f"API response: {response_data}")
```

## 3. Error Handling Standards

### 3.1 Exception Handling Pattern

```python
from typing import Tuple, List, Dict, Any
import traceback

class ProcessingError(Exception):
    """Base exception for processing errors"""
    pass

class ValidationError(ProcessingError):
    """Exception for data validation errors"""
    pass

class APIError(ProcessingError):
    """Exception for API-related errors"""
    pass

def safe_process_item(item: Dict[str, Any], logger: logging.Logger) -> Tuple[bool, Optional[Dict], Optional[str]]:
    """
    Safely process a single item with standardized error handling
    
    Returns:
        Tuple[bool, Optional[Dict], Optional[str]]: (success, processed_item, error_message)
    """
    try:
        # Process item logic here
        processed_item = process_item_logic(item)
        return True, processed_item, None
        
    except ValidationError as e:
        error_msg = f"Validation error: {str(e)}"
        logger.warning(error_msg)
        return False, None, error_msg
        
    except APIError as e:
        error_msg = f"API error: {str(e)}"
        logger.error(error_msg)
        return False, None, error_msg
        
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return False, None, error_msg
```

### 3.2 Return Value Standards

All processing functions must return consistent structures:

**File Processing Functions:**
```python
def process_file(input_file: str, output_file: str, error_file: str) -> ProcessingResult:
    """
    Returns:
        ProcessingResult with success status, counts, and error details
    """
    pass

@dataclass
class ProcessingResult:
    success: bool
    total_processed: int
    success_count: int
    error_count: int
    warning_count: int
    error_details: List[Dict[str, Any]]
    execution_time: float
```

**DataFrame Processing Functions:**
```python
def process_dataframe(df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
    """
    Returns:
        Tuple[pd.DataFrame, List[Dict]]: (success_df, error_records)
    """
    pass
```

## 4. Class Interface Standards

### 4.1 Updater Class Pattern

All updater classes must implement the following interface:

```python
from abc import ABC, abstractmethod
import pandas as pd
from typing import Tuple, List, Dict, Any

class BaseUpdater(ABC):
    """Base class for all updater implementations"""
    
    def __init__(self, batch_size: int = 1000, timeout: int = 30, **kwargs):
        self.batch_size = batch_size
        self.timeout = timeout
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
    @abstractmethod
    async def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """Update DataFrame and return success DataFrame and error records"""
        pass
        
    @abstractmethod
    def update_csv(self, input_file: str, output_file: str, error_file: str) -> ProcessingResult:
        """Update CSV file and save results"""
        pass
        
    def get_statistics(self) -> Dict[str, Any]:
        """Return processing statistics"""
        return {
            'total_processed': self.processed_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': self.success_count / self.processed_count if self.processed_count > 0 else 0
        }
```

## 5. Configuration Management

### 5.1 Configuration Loading

```python
from dataclasses import dataclass
from pathlib import Path
from typing import Optional
import json
import yaml

@dataclass
class ScriptConfig:
    """Base configuration class for scripts"""
    batch_size: int = 1000
    timeout: int = 30
    log_level: str = 'INFO'
    log_dir: Optional[Path] = None
    max_retries: int = 3
    retry_delay: float = 1.0
    
    @classmethod
    def from_file(cls, config_file: Path) -> 'ScriptConfig':
        """Load configuration from file"""
        if config_file.suffix.lower() == '.json':
            with open(config_file, 'r') as f:
                data = json.load(f)
        elif config_file.suffix.lower() in ['.yml', '.yaml']:
            with open(config_file, 'r') as f:
                data = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported config file format: {config_file.suffix}")
            
        return cls(**data)
```

## 6. Backward Compatibility Requirements

### 6.1 API Preservation

- All existing public function signatures must be preserved
- New parameters must have default values
- Deprecated functions should be marked but not removed
- Return value structures must remain compatible

### 6.2 Command-Line Compatibility

- Existing positional argument order must be maintained
- New optional arguments must not conflict with existing ones
- Default behaviors must remain unchanged

## 7. Testing Standards

### 7.1 Unit Test Structure

```python
import unittest
import tempfile
import pandas as pd
from pathlib import Path

class TestScriptName(unittest.TestCase):
    """Test cases for script_name.py"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.test_data = self._create_test_data()
        
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir)
        
    def test_basic_functionality(self):
        """Test basic script functionality"""
        pass
        
    def test_error_handling(self):
        """Test error handling scenarios"""
        pass
        
    def test_backward_compatibility(self):
        """Test backward compatibility with existing APIs"""
        pass
```

This document provides the foundation for standardizing all pipeline scripts while maintaining backward compatibility and ensuring consistent behavior across the system.
