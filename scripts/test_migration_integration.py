#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缓存系统迁移的集成测试

验证所有更新的文件都能正确使用新的优化缓存管理器

作者: Assistant
日期: 2024
"""

from pathlib import Path
import tempfile
import shutil
from optimized_cache_manager import OptimizedBatchCacheManager

def test_cache_manager_instantiation():
    """测试缓存管理器实例化"""
    print("🧪 测试缓存管理器实例化...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir) / "test_cache"
        
        # 测试基本实例化
        cache_manager = OptimizedBatchCacheManager(
            cache_dir=cache_dir,
            memory_ttl=3600,
            file_ttl=86400,
            enable_compression=True
        )
        
        print(f"  ✅ 缓存管理器创建成功")
        print(f"  📁 Brain API缓存文件: {cache_manager.brain_api_cache_file}")
        print(f"  📁 ClickHouse缓存文件: {cache_manager.clickhouse_cache_file}")
        
        # 测试基本操作
        cache_manager.set("test_smiles", {"property": "test_value"}, "brain_api")
        result = cache_manager.get("test_smiles", "brain_api")
        assert result == {"property": "test_value"}, "基本缓存操作失败"
        
        # 测试批量操作
        smiles_list = ["CCO", "CCC", "CCCC"]
        results = {smiles: {"value": f"result_{smiles}"} for smiles in smiles_list}
        batch_key = cache_manager._generate_batch_key(smiles_list)
        
        cache_manager.cache_batch_in_memory(batch_key, smiles_list, results, "brain_api")
        cached_results, missing = cache_manager.get_cached_batch(batch_key, smiles_list, "brain_api")
        
        assert len(cached_results) == len(smiles_list), "批量缓存操作失败"
        assert len(missing) == 0, "批量缓存缺失数据"
        
        # 测试持久化
        cache_manager.persist_cache_to_disk("brain_api")
        assert cache_manager.brain_api_cache_file.exists(), "持久化失败"
        
        print("  ✅ 所有基本操作测试通过")

def test_enhanced_updater_compatibility():
    """测试增强更新器兼容性"""
    print("\n🔧 测试增强更新器兼容性...")
    
    # 测试导入
    try:
        from enhanced_brain_api_updater import EnhancedBrainAPIUpdater
        print("  ✅ EnhancedBrainAPIUpdater 导入成功")
    except ImportError as e:
        print(f"  ❌ EnhancedBrainAPIUpdater 导入失败: {e}")
        return False
    
    try:
        from enhanced_clickhouse_updater import EnhancedClickHouseUpdater
        print("  ✅ EnhancedClickHouseUpdater 导入成功")
    except ImportError as e:
        print(f"  ❌ EnhancedClickHouseUpdater 导入失败: {e}")
        return False
    
    # 测试实例化（不需要实际的API连接）
    with tempfile.TemporaryDirectory() as temp_dir:
        cache_dir = Path(temp_dir) / "test_cache"
        
        try:
            # 测试Brain API更新器
            brain_updater = EnhancedBrainAPIUpdater(
                cache_dir=cache_dir,
                batch_size=100,
                max_concurrent_requests=2
            )
            print("  ✅ EnhancedBrainAPIUpdater 实例化成功")
            
            # 测试ClickHouse更新器
            clickhouse_updater = EnhancedClickHouseUpdater(
                cache_dir=cache_dir,
                batch_size=100
            )
            print("  ✅ EnhancedClickHouseUpdater 实例化成功")
            
        except Exception as e:
            print(f"  ❌ 更新器实例化失败: {e}")
            return False
    
    return True

def test_batch_integration_example():
    """测试批量集成示例"""
    print("\n📦 测试批量集成示例...")
    
    try:
        from optimized_batch_cache_example import OptimizedAPIProcessor
        
        with tempfile.TemporaryDirectory() as temp_dir:
            cache_dir = Path(temp_dir) / "test_cache"
            
            processor = OptimizedAPIProcessor(cache_dir, batch_size=10)
            
            # 测试小批量处理
            test_smiles = ["CCO", "CCC", "CCCC"]
            brain_results = processor.process_smiles_batch(test_smiles, 'brain_api')
            
            assert len(brain_results) == len(test_smiles), "批量处理结果数量不匹配"
            
            # 测试缓存统计
            stats = processor.get_cache_statistics()
            assert 'hit_rate' in stats, "缓存统计信息缺失"
            
            print("  ✅ 批量集成示例测试通过")
            
    except Exception as e:
        print(f"  ❌ 批量集成示例测试失败: {e}")
        return False
    
    return True

def test_file_path_bug_fix():
    """测试文件路径错误修复"""
    print("\n🐛 测试文件路径错误修复...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 使用有问题的路径配置
        problematic_cache_dir = Path(temp_dir) / "cache" / "brain_api"
        
        cache_manager = OptimizedBatchCacheManager(
            cache_dir=problematic_cache_dir,
            enable_file_cache=True
        )
        
        # 设置ClickHouse数据
        cache_manager.set("test_smiles", {"test": "clickhouse_data"}, "clickhouse")
        cache_manager.persist_cache_to_disk("clickhouse")
        
        # 验证文件在正确位置
        expected_clickhouse_file = problematic_cache_dir / "clickhouse_cache.pkl"
        assert expected_clickhouse_file.exists(), f"ClickHouse缓存文件不在预期位置: {expected_clickhouse_file}"
        
        # 验证没有创建错误的文件路径
        wrong_path = problematic_cache_dir / "brain_api" / "clickhouse_cache.pkl"
        assert not wrong_path.exists(), f"错误的文件路径被创建: {wrong_path}"
        
        print("  ✅ 文件路径错误修复验证通过")

def run_migration_tests():
    """运行所有迁移测试"""
    print("🚀 开始缓存系统迁移集成测试")
    print("=" * 50)
    
    tests = [
        test_cache_manager_instantiation,
        test_enhanced_updater_compatibility,
        test_batch_integration_example,
        test_file_path_bug_fix
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            result = test()
            if result is False:
                failed += 1
            else:
                passed += 1
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
            failed += 1
    
    print(f"\n📊 测试结果:")
    print(f"  ✅ 通过: {passed}")
    print(f"  ❌ 失败: {failed}")
    print(f"  📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有迁移测试通过！缓存系统迁移成功！")
        return True
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，需要检查迁移问题")
        return False

if __name__ == "__main__":
    success = run_migration_tests()
    exit(0 if success else 1)
