#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的批量缓存管理器

实现内存优先、批量持久化的缓存策略：
1. 内存优先缓存：在流水线执行期间将所有缓存数据保存在内存中
2. 批量持久化：仅在完整流水线阶段结束时将缓存数据写入磁盘
3. 批量加载：在流水线启动时将整个缓存文件加载到内存中以实现快速查找
4. 两个合并缓存文件：维护两个缓存文件（brain_api_cache.pkl, clickhouse_cache.pkl）

修复了原始实现中的文件路径错误，确保缓存文件始终位于正确的位置。

作者: Assistant
日期: 2024
"""

import pickle
import gzip
import time
import logging
import hashlib
from pathlib import Path
from typing import Any, Dict, Optional, Set, Tuple, List
from dataclasses import dataclass, field
from threading import RLock
from contextlib import contextmanager


class ConsolidatedCache:
    """合并缓存数据结构"""
    def __init__(self):
        self.individual_entries = {}
        self.batch_entries = {}

    def cleanup_expired(self, ttl):
        """清理过期条目（兼容性方法）"""
        # 优化版本不需要清理，因为我们在内存中管理过期
        _ = ttl  # 忽略未使用的参数
        return 0


@dataclass
class CacheEntry:
    """缓存条目"""
    data: Any
    timestamp: float
    access_count: int = 0
    last_access: float = field(default_factory=time.time)

    def is_expired(self, ttl: float) -> bool:
        """检查是否过期"""
        return time.time() - self.timestamp > ttl

    def touch(self):
        """更新访问时间和计数"""
        self.last_access = time.time()
        self.access_count += 1


@dataclass
class BatchCacheEntry:
    """批量缓存条目"""
    smiles_list: List[str]
    results: Dict[str, Any]
    timestamp: float
    access_count: int = 0
    last_access: float = field(default_factory=time.time)

    def is_expired(self, ttl: float) -> bool:
        """检查是否过期"""
        return time.time() - self.timestamp > ttl

    def touch(self):
        """更新访问时间和计数"""
        self.last_access = time.time()
        self.access_count += 1

    def get_results_for_smiles(self, smiles_subset: List[str]) -> Dict[str, Any]:
        """获取指定SMILES子集的结果"""
        return {smiles: self.results[smiles] for smiles in smiles_subset if smiles in self.results}


@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    partial_hits: int = 0
    misses: int = 0
    memory_entries: int = 0
    batch_entries: int = 0
    file_entries: int = 0
    evictions: int = 0

    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.partial_hits + self.misses
        return (self.hits + self.partial_hits) / total if total > 0 else 0.0

    @property
    def full_hit_rate(self) -> float:
        """完全命中率"""
        total = self.hits + self.partial_hits + self.misses
        return self.hits / total if total > 0 else 0.0

    def reset(self):
        """重置统计"""
        self.hits = 0
        self.partial_hits = 0
        self.misses = 0
        self.evictions = 0


class OptimizedBatchCacheManager:
    """
    优化的批量缓存管理器
    
    特性：
    - 内存优先缓存：所有数据首先存储在内存中
    - 批量持久化：仅在阶段结束时写入磁盘
    - 批量加载：启动时加载整个缓存文件到内存
    - 固定的两文件结构：brain_api_cache.pkl 和 clickhouse_cache.pkl
    """

    def __init__(
        self,
        cache_dir: Path,
        memory_ttl: float = 3600,  # 内存缓存TTL (秒)
        file_ttl: float = 86400,   # 文件缓存TTL (秒)
        max_memory_entries: int = 50000,  # 增加内存容量
        enable_compression: bool = True,
        enable_file_cache: bool = True
    ):
        self.cache_dir = Path(cache_dir)
        self.memory_ttl = memory_ttl
        self.file_ttl = file_ttl
        self.max_memory_entries = max_memory_entries
        self.enable_compression = enable_compression
        self.enable_file_cache = enable_file_cache

        # 固定的缓存文件路径 - 修复路径错误
        self.brain_api_cache_file = self.cache_dir / "brain_api_cache.pkl"
        self.clickhouse_cache_file = self.cache_dir / "clickhouse_cache.pkl"

        # 内存缓存存储
        self._brain_api_memory: Dict[str, CacheEntry] = {}
        self._clickhouse_memory: Dict[str, CacheEntry] = {}
        self._brain_api_batches: Dict[str, BatchCacheEntry] = {}
        self._clickhouse_batches: Dict[str, BatchCacheEntry] = {}
        
        # 线程锁
        self._memory_lock = RLock()
        self._brain_api_lock = RLock()
        self._clickhouse_lock = RLock()

        # 统计信息
        self.stats = CacheStats()

        # 日志
        self.logger = logging.getLogger(__name__)

        # 确保缓存目录存在
        if self.enable_file_cache:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"优化缓存目录: {self.cache_dir}")
            
        # 启动时批量加载缓存
        self._bulk_load_caches()

    def _bulk_load_caches(self):
        """启动时批量加载所有缓存文件到内存"""
        if not self.enable_file_cache:
            return
            
        self.logger.info("🚀 批量加载缓存文件到内存...")
        
        # 加载Brain API缓存
        self._bulk_load_cache_file(self.brain_api_cache_file, 'brain_api')
        
        # 加载ClickHouse缓存
        self._bulk_load_cache_file(self.clickhouse_cache_file, 'clickhouse')
        
        with self._memory_lock:
            total_individual = len(self._brain_api_memory) + len(self._clickhouse_memory)
            total_batches = len(self._brain_api_batches) + len(self._clickhouse_batches)
            
        self.logger.info(f"✅ 缓存加载完成: {total_individual} 个体条目, {total_batches} 批量条目")

    def _bulk_load_cache_file(self, cache_file: Path, cache_type: str):
        """批量加载单个缓存文件"""
        if not cache_file.exists():
            self.logger.debug(f"缓存文件不存在: {cache_file}")
            return
            
        try:
            # 加载缓存数据
            if self.enable_compression:
                with gzip.open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
            else:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
            
            # 根据缓存类型选择内存存储
            if cache_type == 'brain_api':
                individual_cache = self._brain_api_memory
                batch_cache = self._brain_api_batches
            else:
                individual_cache = self._clickhouse_memory
                batch_cache = self._clickhouse_batches
            
            with self._memory_lock:
                # 加载个体条目
                if hasattr(cache_data, 'individual_entries'):
                    for key, entry in cache_data.individual_entries.items():
                        if not entry.is_expired(self.file_ttl):
                            # 移除缓存类型前缀
                            clean_key = key.replace(f"{cache_type}:", "")
                            individual_cache[clean_key] = entry
                
                # 加载批量条目
                if hasattr(cache_data, 'batch_entries'):
                    for batch_key, batch_entry in cache_data.batch_entries.items():
                        if not batch_entry.is_expired(self.file_ttl):
                            batch_cache[batch_key] = batch_entry
            
            self.logger.debug(f"加载缓存文件: {cache_file}")
            
        except Exception as e:
            self.logger.warning(f"加载缓存文件失败 {cache_file}: {e}")

    def _generate_batch_key(self, smiles_list: List[str]) -> str:
        """生成批量缓存键"""
        sorted_smiles = sorted(smiles_list)
        combined_string = "|".join(sorted_smiles)
        return hashlib.md5(combined_string.encode('utf-8')).hexdigest()

    def get_cached_batch(self, batch_key: str, smiles_list: List[str], cache_type: str = 'brain_api') -> Tuple[Dict[str, Any], List[str]]:
        """
        获取批量缓存结果（内存优先）
        返回: (缓存的结果字典, 缺失的SMILES列表)
        """
        cached_results = {}
        missing_smiles = []

        # 选择正确的内存缓存
        if cache_type == 'brain_api':
            batch_cache = self._brain_api_batches
            individual_cache = self._brain_api_memory
        else:
            batch_cache = self._clickhouse_batches
            individual_cache = self._clickhouse_memory

        # 检查内存中的批量缓存
        with self._memory_lock:
            if batch_key in batch_cache:
                batch_entry = batch_cache[batch_key]
                if not batch_entry.is_expired(self.memory_ttl):
                    batch_entry.touch()
                    cached_results = batch_entry.get_results_for_smiles(smiles_list)
                    missing_smiles = [s for s in smiles_list if s not in cached_results]

                    if len(cached_results) == len(smiles_list):
                        self.stats.hits += 1
                    elif len(cached_results) > 0:
                        self.stats.partial_hits += 1
                    else:
                        self.stats.misses += 1

                    return cached_results, missing_smiles
                else:
                    # 批量缓存过期，删除
                    del batch_cache[batch_key]
                    self.stats.evictions += 1

            # 如果没有批量缓存，尝试个体SMILES缓存
            for smiles in smiles_list:
                if smiles in individual_cache:
                    entry = individual_cache[smiles]
                    if not entry.is_expired(self.memory_ttl):
                        entry.touch()
                        cached_results[smiles] = entry.data
                    else:
                        del individual_cache[smiles]
                        self.stats.evictions += 1
                        missing_smiles.append(smiles)
                else:
                    missing_smiles.append(smiles)

        if len(cached_results) == len(smiles_list):
            self.stats.hits += 1
        elif len(cached_results) > 0:
            self.stats.partial_hits += 1
        else:
            self.stats.misses += 1

        return cached_results, missing_smiles

    def cache_batch_in_memory(self, batch_key: str, smiles_list: List[str], results: Dict[str, Any], cache_type: str = 'brain_api'):
        """
        在内存中缓存批量结果（不立即写入磁盘）
        """
        # 选择正确的内存缓存
        if cache_type == 'brain_api':
            batch_cache = self._brain_api_batches
            individual_cache = self._brain_api_memory
        else:
            batch_cache = self._clickhouse_batches
            individual_cache = self._clickhouse_memory

        # 创建批量缓存条目
        batch_entry = BatchCacheEntry(
            smiles_list=smiles_list,
            results=results,
            timestamp=time.time()
        )

        with self._memory_lock:
            # 添加批量缓存
            batch_cache[batch_key] = batch_entry

            # 同时添加个体缓存（向后兼容）
            for smiles, result in results.items():
                individual_cache[smiles] = CacheEntry(
                    data=result,
                    timestamp=time.time()
                )

        self.logger.debug(f"内存缓存批量数据: {cache_type}, {len(smiles_list)} SMILES")

    def get(self, key: str, cache_type: str = None) -> Optional[Any]:
        """获取单个缓存数据（内存优先）"""
        # 自动检测缓存类型
        if cache_type is None:
            cache_type = 'brain_api' if 'brain_api' in key else 'clickhouse'
            key = key.replace(f"{cache_type}:", "")  # 移除前缀

        # 选择正确的内存缓存
        if cache_type == 'brain_api':
            individual_cache = self._brain_api_memory
        else:
            individual_cache = self._clickhouse_memory

        with self._memory_lock:
            if key in individual_cache:
                entry = individual_cache[key]
                if not entry.is_expired(self.memory_ttl):
                    entry.touch()
                    self.stats.hits += 1
                    return entry.data
                else:
                    del individual_cache[key]
                    self.stats.evictions += 1

        self.stats.misses += 1
        return None

    def set(self, key: str, data: Any, cache_type: str = None) -> bool:
        """设置单个缓存数据（仅内存，不立即写入磁盘）"""
        # 自动检测缓存类型
        if cache_type is None:
            cache_type = 'brain_api' if 'brain_api' in key else 'clickhouse'
            key = key.replace(f"{cache_type}:", "")  # 移除前缀

        # 选择正确的内存缓存
        if cache_type == 'brain_api':
            individual_cache = self._brain_api_memory
        else:
            individual_cache = self._clickhouse_memory

        cache_entry = CacheEntry(data=data, timestamp=time.time())

        with self._memory_lock:
            individual_cache[key] = cache_entry

        self.logger.debug(f"内存缓存个体数据: {cache_type}:{key}")
        return True

    def persist_cache_to_disk(self, cache_type: str):
        """
        将指定类型的内存缓存批量写入磁盘
        这是批量持久化的核心方法，仅在阶段结束时调用
        """
        if not self.enable_file_cache:
            return

        # 选择缓存文件和内存存储
        if cache_type == 'brain_api':
            cache_file = self.brain_api_cache_file
            individual_cache = self._brain_api_memory
            batch_cache = self._brain_api_batches
            lock = self._brain_api_lock
        else:
            cache_file = self.clickhouse_cache_file
            individual_cache = self._clickhouse_memory
            batch_cache = self._clickhouse_batches
            lock = self._clickhouse_lock

        with lock:
            try:
                # 创建缓存数据结构
                cache_data = ConsolidatedCache()

                with self._memory_lock:
                    # 复制个体条目（添加前缀）
                    for key, entry in individual_cache.items():
                        if not entry.is_expired(self.file_ttl):
                            prefixed_key = f"{cache_type}:{key}"
                            cache_data.individual_entries[prefixed_key] = entry

                    # 复制批量条目
                    for batch_key, batch_entry in batch_cache.items():
                        if not batch_entry.is_expired(self.file_ttl):
                            cache_data.batch_entries[batch_key] = batch_entry

                # 写入磁盘
                self._save_cache_file(cache_file, cache_data)

                entry_count = len(cache_data.individual_entries) + len(cache_data.batch_entries)
                self.logger.info(f"💾 批量持久化完成: {cache_type}, {entry_count} 条目 -> {cache_file}")

            except Exception as e:
                self.logger.error(f"批量持久化失败 {cache_type}: {e}")

    def persist_all_caches(self):
        """将所有内存缓存批量写入磁盘"""
        self.logger.info("🔄 开始批量持久化所有缓存...")
        self.persist_cache_to_disk('brain_api')
        self.persist_cache_to_disk('clickhouse')
        self.logger.info("✅ 所有缓存批量持久化完成")

    def _save_cache_file(self, file_path: Path, cache_data):
        """保存缓存文件"""
        try:
            # 使用临时文件确保原子写入
            temp_file = file_path.with_suffix('.tmp')

            if self.enable_compression:
                with gzip.open(temp_file, 'wb') as f:
                    pickle.dump(cache_data, f)
            else:
                with open(temp_file, 'wb') as f:
                    pickle.dump(cache_data, f)

            # 原子性地替换原文件
            temp_file.replace(file_path)

        except Exception as e:
            self.logger.error(f"保存缓存文件失败 {file_path}: {e}")
            # 清理临时文件
            if temp_file.exists():
                temp_file.unlink(missing_ok=True)
            raise

    def get_missing_smiles(self, smiles_list: List[str], cache_type: str = 'brain_api') -> List[str]:
        """获取未缓存的SMILES列表"""
        missing_smiles = []

        # 选择正确的内存缓存
        if cache_type == 'brain_api':
            individual_cache = self._brain_api_memory
        else:
            individual_cache = self._clickhouse_memory

        with self._memory_lock:
            for smiles in smiles_list:
                if smiles not in individual_cache or individual_cache[smiles].is_expired(self.memory_ttl):
                    missing_smiles.append(smiles)

        return missing_smiles

    def clear(self):
        """清空所有缓存"""
        with self._memory_lock:
            self._brain_api_memory.clear()
            self._clickhouse_memory.clear()
            self._brain_api_batches.clear()
            self._clickhouse_batches.clear()

        # 清空文件缓存
        if self.enable_file_cache:
            for cache_file in [self.brain_api_cache_file, self.clickhouse_cache_file]:
                if cache_file.exists():
                    cache_file.unlink(missing_ok=True)

        # 重置统计
        self.stats.reset()
        self.logger.info("所有缓存已清空")

    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._memory_lock:
            self.stats.memory_entries = (len(self._brain_api_memory) + len(self._clickhouse_memory))
            self.stats.batch_entries = (len(self._brain_api_batches) + len(self._clickhouse_batches))

        return self.stats

    def cleanup_expired(self):
        """清理过期的内存缓存条目"""
        with self._memory_lock:
            # 清理Brain API个体缓存
            expired_keys = [k for k, v in self._brain_api_memory.items() if v.is_expired(self.memory_ttl)]
            for key in expired_keys:
                del self._brain_api_memory[key]
                self.stats.evictions += 1

            # 清理ClickHouse个体缓存
            expired_keys = [k for k, v in self._clickhouse_memory.items() if v.is_expired(self.memory_ttl)]
            for key in expired_keys:
                del self._clickhouse_memory[key]
                self.stats.evictions += 1

            # 清理Brain API批量缓存
            expired_keys = [k for k, v in self._brain_api_batches.items() if v.is_expired(self.memory_ttl)]
            for key in expired_keys:
                del self._brain_api_batches[key]
                self.stats.evictions += 1

            # 清理ClickHouse批量缓存
            expired_keys = [k for k, v in self._clickhouse_batches.items() if v.is_expired(self.memory_ttl)]
            for key in expired_keys:
                del self._clickhouse_batches[key]
                self.stats.evictions += 1

        self.logger.debug("过期缓存清理完成")


# 向后兼容的别名
OptimizedCacheManager = OptimizedBatchCacheManager
