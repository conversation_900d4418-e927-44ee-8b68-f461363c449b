#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化验证脚本

快速验证优化功能是否正常工作的脚本。

作者: Assistant
日期: 2024
"""

import asyncio
import sys
import os
import time
from pathlib import Path

# 添加当前脚本目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    missing_deps = []
    
    try:
        import pandas as pd
        print("✅ pandas")
    except ImportError:
        missing_deps.append("pandas")
        print("❌ pandas")
    
    try:
        import asyncio
        print("✅ asyncio")
    except ImportError:
        missing_deps.append("asyncio")
        print("❌ asyncio")
    
    # 检查优化模块
    optimization_modules = [
        "optimized_cache_manager",
        "smiles_deduplicator",
        "batch_processor",
        "concurrent_processor",
        "enhanced_brain_api_updater",
        "enhanced_clickhouse_updater"
    ]
    
    for module in optimization_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            missing_deps.append(module)
            print(f"❌ {module}: {e}")
    
    if missing_deps:
        print(f"\n❌ 缺少依赖项: {', '.join(missing_deps)}")
        return False
    else:
        print("\n✅ 所有依赖项检查通过")
        return True


def check_test_data():
    """检查测试数据"""
    print("\n🔍 检查测试数据...")
    
    test_data_dir = Path("test_data")
    if not test_data_dir.exists():
        print("❌ 测试数据目录不存在")
        print("请运行: ./prepare_test_data.sh")
        return False
    
    test_files = list(test_data_dir.glob("*.jsonl"))
    if not test_files:
        print("❌ 没有找到测试文件")
        return False
    
    print(f"✅ 找到 {len(test_files)} 个测试文件")
    for file in sorted(test_files, key=lambda x: x.stat().st_size):
        size_kb = file.stat().st_size / 1024
        print(f"   📁 {file.name}: {size_kb:.1f} KB")
    
    return True


async def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试基本功能...")
    
    try:
        # 测试缓存管理器
        from optimized_cache_manager import OptimizedBatchCacheManager as CacheManager
        cache_dir = Path("test_cache")
        cache_dir.mkdir(exist_ok=True)
        
        cache = CacheManager(cache_dir)
        cache.set("test_key", "test_value")
        value = cache.get("test_key")
        
        if value == "test_value":
            print("✅ 缓存管理器")
        else:
            print("❌ 缓存管理器")
            return False
        
        # 测试SMILES去重器
        from smiles_deduplicator import SmilesDeduplicator
        import pandas as pd
        
        deduplicator = SmilesDeduplicator()
        test_df = pd.DataFrame({
            'canonical_smiles': ['CC', 'CCO', 'CC', 'CCC', 'CCO']
        })
        
        unique_smiles, mapping = deduplicator.deduplicate_dataframe(test_df)
        
        if len(unique_smiles) == 3:  # CC, CCO, CCC
            print("✅ SMILES去重器")
        else:
            print("❌ SMILES去重器")
            return False
        
        # 测试批处理器
        from batch_processor import BatchProcessor, BatchConfig
        
        config = BatchConfig(batch_size=2, max_concurrent=1)
        processor = BatchProcessor(config)
        
        async def dummy_processor(batch):
            return batch, []
        
        items = [1, 2, 3, 4, 5]
        success, errors = await processor.process_batches(items, dummy_processor)
        
        if len(success) == 5 and len(errors) == 0:
            print("✅ 批处理器")
        else:
            print("❌ 批处理器")
            return False
        
        print("✅ 所有基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False


async def test_pipeline_integration():
    """测试流水线集成"""
    print("\n🔍 测试流水线集成...")
    
    try:
        from pipeline_config import PipelineConfig
        from modular_pipeline import ModularPipeline
        
        # 查找最小的测试文件
        test_data_dir = Path("test_data")
        test_files = list(test_data_dir.glob("*.jsonl"))
        if not test_files:
            print("❌ 没有测试文件")
            return False
        
        test_files.sort(key=lambda x: x.stat().st_size)
        test_file = test_files[0]
        
        print(f"📁 使用测试文件: {test_file.name}")
        
        # 创建配置
        output_dir = Path("validation_output")
        config = PipelineConfig(
            input_file=test_file,
            output_dir=output_dir / "output",
            temp_dir=output_dir / "temp", 
            log_dir=output_dir / "logs",
            cache_dir=output_dir / "cache",
            enable_optimizations=True,
            brain_api_batch_size=50,  # 小批次用于快速测试
            clickhouse_batch_size=50,
            max_concurrent_requests=1  # 单线程避免复杂性
        )
        
        # 创建流水线
        pipeline = ModularPipeline(config)
        
        # 检查优化组件是否正确初始化
        if not hasattr(pipeline, 'enhanced_brain_updater'):
            print("❌ 优化组件未初始化")
            return False
        
        if pipeline.enhanced_brain_updater is None:
            print("⚠️ Brain API优化器未初始化（可能是导入问题）")
        else:
            print("✅ Brain API优化器已初始化")
        
        if pipeline.enhanced_clickhouse_updater is None:
            print("⚠️ ClickHouse优化器未初始化（可能是导入问题）")
        else:
            print("✅ ClickHouse优化器已初始化")
        
        print("✅ 流水线集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 流水线集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def print_summary(results):
    """打印测试摘要"""
    print("\n" + "=" * 60)
    print("优化验证摘要")
    print("=" * 60)
    
    all_passed = all(results.values())
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 所有验证测试通过！优化功能已就绪。")
        print("\n下一步:")
        print("1. 运行完整测试: python3 run_optimization_tests.py")
        print("2. 使用优化流水线: python3 modular_pipeline.py input.jsonl --optimized")
    else:
        print("💥 部分验证测试失败。请检查错误信息并修复问题。")
        print("\n故障排除:")
        print("1. 确保所有依赖项已安装")
        print("2. 检查优化模块文件是否存在")
        print("3. 查看详细错误信息")
    
    print("=" * 60)
    
    return all_passed


async def main():
    """主函数"""
    print("🚀 开始优化功能验证")
    print("=" * 60)
    
    results = {}
    
    # 检查依赖项
    results["依赖项检查"] = check_dependencies()
    
    # 检查测试数据
    results["测试数据检查"] = check_test_data()
    
    # 测试基本功能
    if results["依赖项检查"]:
        results["基本功能测试"] = await test_basic_functionality()
    else:
        results["基本功能测试"] = False
    
    # 测试流水线集成
    if results["基本功能测试"]:
        results["流水线集成测试"] = await test_pipeline_integration()
    else:
        results["流水线集成测试"] = False
    
    # 打印摘要
    success = print_summary(results)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
