#!/usr/bin/env python3
"""
模块化数据处理流水线
基于阶段一的配置，支持DataFrame流式处理

作者: Assistant
日期: 2024
"""

import asyncio
import logging
import pandas as pd
import time
import psutil
import os
import sys
import io
from contextlib import redirect_stdout, redirect_stderr
from typing import Dict, List, Tuple
from dataclasses import dataclass

# 导入阶段一的配置
from pipeline_config import PipelineConfig

# 导入各个处理模块
from convert_jsonl_to_csv import JSONLToCSVConverter
from update_csv_with_brain_api import CSVUpdater as BrainAPIUpdater
from update_csv_with_clickhouse import CSVUpdater as C<PERSON><PERSON>ouseUpdater
from deduplicate_and_update_prices import deduplicate_and_update_dataframe


@dataclass
class ModularPipelineStats:
    """模块化流水线统计信息"""

    total_start_time: float
    total_end_time: float
    step_times: Dict[str, float]
    memory_usage: Dict[str, float]
    data_counts: Dict[str, int]
    error_counts: Dict[str, int]

    @property
    def total_duration(self) -> float:
        return self.total_end_time - self.total_start_time

    def get_step_duration(self, step: str) -> float:
        return self.step_times.get(step, 0.0)


class ModularPipelineMonitor:
    """模块化流水线监控器"""

    def __init__(self):
        self.stats = ModularPipelineStats(
            total_start_time=0,
            total_end_time=0,
            step_times={},
            memory_usage={},
            data_counts={},
            error_counts={},
        )
        self.step_start_times = {}

    def start_pipeline(self):
        """开始流水线监控"""
        self.stats.total_start_time = time.time()
        self._log_memory_usage("pipeline_start")

    def end_pipeline(self):
        """结束流水线监控"""
        self.stats.total_end_time = time.time()
        self._log_memory_usage("pipeline_end")

    def start_step(self, step_name: str):
        """开始步骤监控"""
        self.step_start_times[step_name] = time.time()
        self._log_memory_usage(f"{step_name}_start")

    def end_step(self, step_name: str, data_count: int = 0, error_count: int = 0):
        """结束步骤监控"""
        if step_name in self.step_start_times:
            duration = time.time() - self.step_start_times[step_name]
            self.stats.step_times[step_name] = duration
            self.stats.data_counts[step_name] = data_count
            self.stats.error_counts[step_name] = error_count
            self._log_memory_usage(f"{step_name}_end")

    def _log_memory_usage(self, checkpoint: str):
        """记录内存使用情况"""
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        self.stats.memory_usage[checkpoint] = memory_mb

    def print_summary(self):
        """打印监控摘要"""
        print("\n" + "=" * 60)
        print("模块化流水线执行摘要")
        print("=" * 60)
        print(f"总执行时间: {self.stats.total_duration:.2f} 秒")
        print(
            f"内存使用: {self.stats.memory_usage.get('pipeline_start', 0):.1f} MB -> {self.stats.memory_usage.get('pipeline_end', 0):.1f} MB"
        )
        print("\n各步骤详情:")

        for step, duration in self.stats.step_times.items():
            data_count = self.stats.data_counts.get(step, 0)
            error_count = self.stats.error_counts.get(step, 0)
            success_rate = (
                ((data_count - error_count) / data_count * 100) if data_count > 0 else 0
            )
            print(
                f"  {step}: {duration:.2f}s | 数据: {data_count} | 错误: {error_count} | 成功率: {success_rate:.1f}%"
            )

        print("=" * 60)


class ModularPipeline:
    """模块化数据处理流水线"""

    def __init__(self, config: PipelineConfig):
        self.config = config
        self.monitor = ModularPipelineMonitor()
        self.logger = self._setup_logging()
        
        # 用于捕获子模块输出的缓冲区
        self.output_buffer = io.StringIO()
        self.error_buffer = io.StringIO()

        # 准备目录
        self.prepare_directories()

        # 配置子模块日志 - 必须在导入增强模块之前配置
        self._setup_submodule_logging()

        # 初始化各个处理器
        self.jsonl_converter = JSONLToCSVConverter()
        self.brain_updater = BrainAPIUpdater(batch_size=config.brain_api_batch_size)
        self.clickhouse_updater = ClickHouseUpdater(
            clickhouse_host=config.clickhouse_host
        )

        # 初始化优化处理器（如果启用优化）
        if config.enable_optimizations:
            try:
                from enhanced_brain_api_updater import EnhancedBrainAPIUpdater
                from enhanced_clickhouse_updater import EnhancedClickHouseUpdater

                self.enhanced_brain_updater = EnhancedBrainAPIUpdater(config)
                self.enhanced_clickhouse_updater = EnhancedClickHouseUpdater(config)
                self.logger.info("优化处理器初始化完成")

                # 重新配置增强模块的日志记录器，确保它们有正确的处理器
                self._reconfigure_enhanced_module_loggers()
            except ImportError as e:
                self.logger.warning(f"无法导入优化模块: {e}")
                self.logger.warning("将使用标准处理器")
                self.enhanced_brain_updater = None
                self.enhanced_clickhouse_updater = None
        else:
            self.enhanced_brain_updater = None
            self.enhanced_clickhouse_updater = None

    def prepare_directories(self):
        """准备输出目录"""
        for directory in [
            self.config.output_dir,
            self.config.temp_dir,
            self.config.log_dir,
        ]:
            directory.mkdir(parents=True, exist_ok=True)

    def get_file_paths(self) -> Dict[str, str]:
        """获取各步骤的文件路径"""
        return {
            "step1_errors": str(self.config.temp_dir / "conversion_errors.log"),
            "step1_warnings": str(self.config.temp_dir / "conversion_warnings.log"),
            "step1_stats": str(self.config.temp_dir / "conversion_stats.log"),
            "step2_errors": str(self.config.temp_dir / "brain_errors.csv"),
            "step3_errors": str(self.config.temp_dir / "clickhouse_errors.csv"),
            "final_csv": str(self.config.output_dir / "final_processed.csv"),
            "duplicates_log": str(self.config.output_dir / "duplicates.log"),
            "warnings_csv": str(self.config.output_dir / "warnings.csv"),
            "temp_step1_csv": str(self.config.temp_dir / "material_items.csv"),
            "temp_step2_csv": str(
                self.config.temp_dir / "material_items_brain_updated.csv"
            ),
            "temp_step3_csv": str(
                self.config.temp_dir / "material_items_clickhouse_updated.csv"
            ),
            "pipeline_log": str(self.config.log_dir / "modular_pipeline.log"),
            "full_execution_log": str(self.config.log_dir / "full_execution.log"),
        }

    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("modular_pipeline")
        logger.setLevel(getattr(logging, self.config.log_level))

        # 清除现有的处理器以避免重复
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(self.config.log_format)
        
        # 控制台处理器 - 显示原始脚本的日志输出
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(getattr(logging, self.config.log_level))
        logger.addHandler(console_handler)
        
        # 确保日志目录存在
        self.config.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 文件处理器 - 保存所有运行日志到特定文件
        log_file_path = self.config.log_dir / "modular_pipeline.log"
        file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)  # 文件中记录所有级别的日志
        logger.addHandler(file_handler)
        
        # 完整执行日志处理器 - 包含详细的执行信息
        full_log_path = self.config.log_dir / "full_execution.log"
        full_handler = logging.FileHandler(full_log_path, mode='w', encoding='utf-8')
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        full_handler.setFormatter(detailed_formatter)
        full_handler.setLevel(logging.DEBUG)
        logger.addHandler(full_handler)
        
        # 记录日志设置信息
        logger.info(f"日志系统已初始化")
        logger.info(f"控制台日志级别: {self.config.log_level}")
        logger.info(f"文件日志保存到: {log_file_path}")
        logger.info(f"详细执行日志保存到: {full_log_path}")
        
        return logger
    
    def _setup_submodule_logging(self):
        """配置子模块的日志记录器"""
        # 基础子模块日志记录器
        base_submodule_loggers = [
            'convert_jsonl_to_csv',
            'update_csv_with_brain_api',
            'update_csv_with_clickhouse',
            'deduplicate_and_update_prices'
        ]

        # 增强模块日志记录器（如果启用优化）
        enhanced_submodule_loggers = []
        if self.config.enable_optimizations:
            enhanced_submodule_loggers = [
                'enhanced_brain_api_updater',
                'enhanced_clickhouse_updater',
                'optimized_cache_manager',
                'smiles_deduplicator',
                'batch_processor',
                'concurrent_processor'
            ]

        # 合并所有需要配置的日志记录器
        all_submodule_loggers = base_submodule_loggers + enhanced_submodule_loggers

        for logger_name in all_submodule_loggers:
            sublogger = logging.getLogger(logger_name)
            sublogger.setLevel(logging.DEBUG)

            # 清除现有处理器
            sublogger.handlers.clear()

            # 确保日志目录存在
            self.config.log_dir.mkdir(parents=True, exist_ok=True)

            # 添加控制台处理器 - 确保子模块日志在控制台可见
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(f'%(asctime)s - {logger_name} - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            console_handler.setLevel(getattr(logging, self.config.log_level))
            sublogger.addHandler(console_handler)

            # 添加文件处理器，将子模块日志也保存到主日志文件
            log_file_path = self.config.log_dir / "modular_pipeline.log"
            file_handler = logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
            file_formatter = logging.Formatter(f'%(asctime)s - {logger_name} - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            file_handler.setLevel(logging.DEBUG)  # 文件中记录所有级别的日志
            sublogger.addHandler(file_handler)

            # 添加详细日志处理器
            full_log_path = self.config.log_dir / "full_execution.log"
            full_handler = logging.FileHandler(full_log_path, mode='a', encoding='utf-8')
            detailed_formatter = logging.Formatter(
                f'%(asctime)s - {logger_name} - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            full_handler.setFormatter(detailed_formatter)
            full_handler.setLevel(logging.DEBUG)
            sublogger.addHandler(full_handler)

            # 禁用传播以避免重复日志（因为我们已经添加了控制台处理器）
            sublogger.propagate = False

            # 调试信息：记录配置的处理器数量
            self.logger.debug(f"配置 {logger_name} 日志记录器: {len(sublogger.handlers)} 个处理器")

        self.logger.info(f"子模块日志记录器配置完成，共配置 {len(all_submodule_loggers)} 个模块")
        if enhanced_submodule_loggers:
            self.logger.info(f"增强模块日志记录器: {', '.join(enhanced_submodule_loggers)}")

    def _reconfigure_enhanced_module_loggers(self):
        """重新配置增强模块的日志记录器，确保它们有正确的处理器"""
        enhanced_loggers = [
            'enhanced_brain_api_updater',
            'enhanced_clickhouse_updater',
            'optimized_cache_manager',
            'smiles_deduplicator',
            'batch_processor',
            'concurrent_processor'
        ]

        for logger_name in enhanced_loggers:
            sublogger = logging.getLogger(logger_name)

            # 检查是否已经有正确的处理器
            has_file_handler = any(isinstance(h, logging.FileHandler) for h in sublogger.handlers)

            if not has_file_handler:
                self.logger.debug(f"重新配置 {logger_name} 日志记录器")

                # 清除现有处理器
                sublogger.handlers.clear()
                sublogger.setLevel(logging.DEBUG)

                # 确保日志目录存在
                self.config.log_dir.mkdir(parents=True, exist_ok=True)

                # 添加控制台处理器
                console_handler = logging.StreamHandler()
                console_formatter = logging.Formatter(f'%(asctime)s - {logger_name} - %(levelname)s - %(message)s')
                console_handler.setFormatter(console_formatter)
                console_handler.setLevel(getattr(logging, self.config.log_level))
                sublogger.addHandler(console_handler)

                # 添加文件处理器
                log_file_path = self.config.log_dir / "modular_pipeline.log"
                file_handler = logging.FileHandler(log_file_path, mode='a', encoding='utf-8')
                file_formatter = logging.Formatter(f'%(asctime)s - {logger_name} - %(levelname)s - %(message)s')
                file_handler.setFormatter(file_formatter)
                file_handler.setLevel(logging.DEBUG)
                sublogger.addHandler(file_handler)

                # 添加详细日志处理器
                full_log_path = self.config.log_dir / "full_execution.log"
                full_handler = logging.FileHandler(full_log_path, mode='a', encoding='utf-8')
                detailed_formatter = logging.Formatter(
                    f'%(asctime)s - {logger_name} - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
                )
                full_handler.setFormatter(detailed_formatter)
                full_handler.setLevel(logging.DEBUG)
                sublogger.addHandler(full_handler)

                # 禁用传播以避免重复日志
                sublogger.propagate = False

                self.logger.debug(f"重新配置完成 {logger_name}: {len(sublogger.handlers)} 个处理器")

        self.logger.info("增强模块日志记录器重新配置完成")
    
    def _log_system_info(self):
        """记录系统信息和配置"""
        import platform
        from datetime import datetime
        
        self.logger.info("=" * 80)
        self.logger.info("模块化数据处理流水线启动")
        self.logger.info("=" * 80)
        self.logger.info(f"启动时间: {datetime.now()}")
        self.logger.info(f"Python版本: {platform.python_version()}")
        self.logger.info(f"操作系统: {platform.system()} {platform.release()}")
        self.logger.info(f"处理器: {platform.processor()}")
        
        # 记录内存信息
        process = psutil.Process()
        memory_info = process.memory_info()
        self.logger.info(f"当前内存使用: {memory_info.rss / 1024 / 1024:.1f} MB")
        
        # 记录配置信息
        self.logger.info("\n配置信息:")
        self.logger.info(f"  输入文件: {self.config.input_file}")
        self.logger.info(f"  输出目录: {self.config.output_dir}")
        self.logger.info(f"  临时目录: {self.config.temp_dir}")
        self.logger.info(f"  日志目录: {self.config.log_dir}")
        self.logger.info(f"  日志级别: {self.config.log_level}")
        self.logger.info(f"  Brain API批处理大小: {self.config.brain_api_batch_size}")
        self.logger.info(f"  ClickHouse主机: {self.config.clickhouse_host}")
        self.logger.info(f"  清理临时文件: {self.config.cleanup_temp}")
        self.logger.info(f"  步骤超时时间: {self.config.step_timeout}秒")
        self.logger.info("=" * 80)
    
    def _capture_and_log_output(self, func, *args, **kwargs):
        """捕获函数输出并记录到日志"""
        # 重置缓冲区
        self.output_buffer.seek(0)
        self.output_buffer.truncate(0)
        self.error_buffer.seek(0)
        self.error_buffer.truncate(0)
        
        try:
            # 捕获标准输出和标准错误
            with redirect_stdout(self.output_buffer), redirect_stderr(self.error_buffer):
                result = func(*args, **kwargs)
            
            # 获取捕获的输出
            stdout_content = self.output_buffer.getvalue()
            stderr_content = self.error_buffer.getvalue()
            
            # 将捕获的输出记录到日志并显示到控制台
            if stdout_content:
                for line in stdout_content.strip().split('\n'):
                    if line.strip():
                        print(line)  # 显示原始输出
                        self.logger.info(f"[子模块输出] {line}")
            
            if stderr_content:
                for line in stderr_content.strip().split('\n'):
                    if line.strip():
                        print(line, file=sys.stderr)  # 显示原始错误输出
                        self.logger.warning(f"[子模块错误] {line}")
            
            return result
            
        except Exception as e:
            # 记录异常信息
            self.logger.error(f"函数执行异常: {e}")
            # 获取可能的输出
            stdout_content = self.output_buffer.getvalue()
            stderr_content = self.error_buffer.getvalue()
            
            if stdout_content:
                self.logger.info(f"异常前的输出: {stdout_content}")
            if stderr_content:
                self.logger.error(f"异常前的错误: {stderr_content}")
            
            raise

    async def run_pipeline(
        self, input_jsonl_file: str
    ) -> Tuple[pd.DataFrame, Dict[str, List]]:
        """运行完整的模块化流水线

        Args:
            input_jsonl_file: 输入的JSONL文件路径

        Returns:
            Tuple[pd.DataFrame, Dict[str, List]]: (最终处理的DataFrame, 各步骤的错误记录)
        """
        self.monitor.start_pipeline()
        
        # 记录系统信息和配置
        self._log_system_info()
        
        self.logger.info(f"开始模块化流水线处理: {input_jsonl_file}")
        
        # 检查输入文件
        from pathlib import Path
        input_path = Path(input_jsonl_file)
        if not input_path.exists():
            self.logger.error(f"输入文件不存在: {input_jsonl_file}")
            raise FileNotFoundError(f"输入文件不存在: {input_jsonl_file}")
        
        file_size_mb = input_path.stat().st_size / 1024 / 1024
        self.logger.info(f"输入文件大小: {file_size_mb:.1f} MB")

        # 获取文件路径
        paths = self.get_file_paths()

        all_errors = {
            "jsonl_conversion": [],
            "brain_api_update": [],
            "clickhouse_update": [],
            "deduplication": [],
        }

        try:
            # 步骤1: JSONL转换为DataFrame
            self.monitor.start_step("jsonl_conversion")
            self.logger.info("步骤1: 转换JSONL文件为DataFrame")
            self.logger.info(f"正在处理文件: {input_jsonl_file}")

            success_df, error_records, warning_records = self._capture_and_log_output(
                self.jsonl_converter.convert_jsonl_to_dataframe, input_jsonl_file, str(self.config.temp_dir)
            )
            all_errors["jsonl_conversion"].extend(error_records)
            all_errors["jsonl_conversion"].extend(warning_records)

            # 生成详细的统计报告
            self.logger.info("生成JSONL转换统计报告...")
            self._generate_conversion_stats_report(
                success_df, error_records, warning_records, paths["step1_stats"]
            )
            self.logger.info("统计报告生成完成")

            # 保存步骤1的中间结果和错误日志
            if not success_df.empty:
                self.logger.info("保存步骤1中间结果...")
                success_df.to_csv(paths["temp_step1_csv"], index=False)
                self.logger.info(f"步骤1中间结果已保存到: {paths['temp_step1_csv']}")

            self.logger.info("保存步骤1错误和警告日志...")
            self._save_step_errors(
                error_records, paths["step1_errors"], "conversion_errors"
            )
            self._save_step_errors(
                warning_records, paths["step1_warnings"], "conversion_warnings"
            )
            self.logger.info("步骤1日志保存完成")

            self.monitor.end_step(
                "jsonl_conversion",
                len(success_df),
                len(error_records) + len(warning_records),
            )
            self.logger.info(
                f"JSONL转换完成: 成功 {len(success_df)} 行, 错误/警告 {len(error_records) + len(warning_records)} 条"
            )

            if success_df.empty:
                self.logger.warning("JSONL转换后没有有效数据，流水线终止")
                return success_df, all_errors

            # 步骤2: Brain API更新SMILES
            self.monitor.start_step("brain_api_update")
            self.logger.info("步骤2: 使用Brain API更新SMILES")
            self.logger.info(f"待更新数据行数: {len(success_df)}")

            # 注意：对于异步函数，我们需要特殊处理
            self.logger.info("开始Brain API更新...")
            updated_df, brain_errors = await self.brain_updater.update_dataframe(
                success_df
            )
            self.logger.info("Brain API更新完成")
            all_errors["brain_api_update"].extend(brain_errors)

            # 保存步骤2的中间结果和错误日志
            if not updated_df.empty:
                self.logger.info("保存步骤2中间结果...")
                updated_df.to_csv(paths["temp_step2_csv"], index=False)
                self.logger.info(f"步骤2中间结果已保存到: {paths['temp_step2_csv']}")

            self.logger.info("保存步骤2错误日志...")
            self._save_step_errors(brain_errors, paths["step2_errors"], "brain_errors")
            self.logger.info("步骤2日志保存完成")

            self.monitor.end_step(
                "brain_api_update", len(updated_df), len(brain_errors)
            )
            self.logger.info(
                f"Brain API更新完成: 成功 {len(updated_df)} 行, 错误 {len(brain_errors)} 条"
            )

            # 步骤3: ClickHouse更新hazard信息
            self.monitor.start_step("clickhouse_update")
            self.logger.info("步骤3: 使用ClickHouse更新hazard信息")
            self.logger.info(f"待更新数据行数: {len(updated_df)}")

            hazard_updated_df, clickhouse_errors = self._capture_and_log_output(
                self.clickhouse_updater.update_dataframe, updated_df
            )
            all_errors["clickhouse_update"].extend(clickhouse_errors)

            # 保存步骤3的中间结果和错误日志
            if not hazard_updated_df.empty:
                self.logger.info("保存步骤3中间结果...")
                hazard_updated_df.to_csv(paths["temp_step3_csv"], index=False)
                self.logger.info(f"步骤3中间结果已保存到: {paths['temp_step3_csv']}")

            self.logger.info("保存步骤3错误日志...")
            self._save_step_errors(
                clickhouse_errors, paths["step3_errors"], "clickhouse_errors"
            )
            self.logger.info("步骤3日志保存完成")

            self.monitor.end_step(
                "clickhouse_update", len(hazard_updated_df), len(clickhouse_errors)
            )
            self.logger.info(
                f"ClickHouse更新完成: 成功 {len(hazard_updated_df)} 行, 错误 {len(clickhouse_errors)} 条"
            )

            # 步骤4: 去重和价格更新
            self.monitor.start_step("deduplication")
            self.logger.info("步骤4: 去重和价格更新")
            self.logger.info(f"去重前数据行数: {len(hazard_updated_df)}")

            final_df, dedup_records = self._capture_and_log_output(
                deduplicate_and_update_dataframe, hazard_updated_df
            )
            all_errors["deduplication"].extend(dedup_records)

            # 保存最终结果
            if not final_df.empty:
                self.logger.info("保存最终处理结果...")
                final_df.to_csv(paths["final_csv"], index=False)
                self.logger.info(f"最终结果已保存到: {paths['final_csv']}")

            # 保存去重日志
            self.logger.info("保存去重日志...")
            self._save_step_errors(dedup_records, paths["duplicates_log"], "duplicates")
            self.logger.info("步骤4日志保存完成")

            self.monitor.end_step("deduplication", len(final_df), len(dedup_records))
            self.logger.info(
                f"去重和价格更新完成: 最终 {len(final_df)} 行, 记录 {len(dedup_records)} 条"
            )

            return final_df, all_errors

        except Exception as e:
            self.logger.error(f"流水线执行出错: {e}")
            self.logger.exception("详细错误信息:")
            raise
        finally:
            self.monitor.end_pipeline()
            self.monitor.print_summary()
            self.print_final_summary(paths)
            
            # 记录最终统计信息
            self.logger.info("\n" + "=" * 80)
            self.logger.info("流水线执行完成")
            self.logger.info(f"总执行时间: {self.monitor.stats.total_duration:.2f} 秒")
            self.logger.info(f"最终内存使用: {self.monitor.stats.memory_usage.get('pipeline_end', 0):.1f} MB")
            self.logger.info("所有日志已保存到文件")
            self.logger.info("=" * 80)

    def _generate_conversion_stats_report(self, success_df, error_records, warning_records, stats_file_path: str):
        """生成JSONL转换的详细统计报告"""
        try:
            from datetime import datetime
            
            # 计算基本统计
            # 注意：total_lines应该是原始输入文件的实际行数
            # 这里我们需要从转换器获取实际处理的行数
            if hasattr(self.jsonl_converter, 'total_processed_lines'):
                total_lines = self.jsonl_converter.total_processed_lines
            else:
                # 回退方案：成功行数 + 错误记录数（这可能不准确）
                total_lines = len(success_df) + len(error_records)
            
            success_count = len(success_df)
            error_count = len(error_records)
            warning_count = len(warning_records)
            success_rate = (success_count / total_lines * 100) if total_lines > 0 else 0

            # 统计错误类型
            error_stats = {}
            for error_record in error_records:
                errors = error_record.get('errors', [])
                for error in errors:
                    error_type = error.split(':')[0] if ':' in error else error
                    error_stats[error_type] = error_stats.get(error_type, 0) + 1

            # 统计警告类型
            warning_stats = {}
            for warning_record in warning_records:
                warnings = warning_record.get('warnings', [])
                for warning in warnings:
                    warning_type = warning.split(':')[0] if ':' in warning else warning
                    warning_stats[warning_type] = warning_stats.get(warning_type, 0) + 1

            # 生成统计报告
            stats_report = []
            stats_report.append(f"\n✅ 转换完成!")
            stats_report.append(f"📊 统计信息:")
            stats_report.append(f"  - 总处理行数: {total_lines:,}")
            stats_report.append(f"  - 成功转换: {success_count:,}")
            stats_report.append(f"  - 错误行数: {error_count:,}")
            stats_report.append(f"  - 警告行数: {warning_count:,}")
            stats_report.append(f"  - 成功率: {success_rate:.2f}%")

            # 错误统计
            if error_stats:
                stats_report.append(f"\n❌ 错误类型统计:")
                for error_type, count in sorted(error_stats.items(), key=lambda x: x[1], reverse=True):
                    stats_report.append(f"  - {error_type}: {count:,} 次")
            
            # 详细错误信息（显示前10个错误记录）
            if error_records:
                stats_report.append(f"\n📋 错误详情 (前10条):")
                for i, error_record in enumerate(error_records[:10]):
                    line_num = error_record.get('line_number', '未知')
                    errors = error_record.get('errors', [])
                    if isinstance(errors, list):
                        error_msg = '; '.join(errors[:2])  # 只显示前2个错误
                    else:
                        error_msg = str(errors)[:100]  # 限制长度
                    stats_report.append(f"  {i+1}. 第{line_num}行: {error_msg}")
                
                if len(error_records) > 10:
                    stats_report.append(f"  ... 还有 {len(error_records) - 10} 条错误记录")

            # 警告统计
            if warning_stats:
                stats_report.append(f"\n⚠️ 警告类型统计:")
                for warning_type, count in sorted(warning_stats.items(), key=lambda x: x[1], reverse=True):
                    stats_report.append(f"  - {warning_type}: {count:,} 次")

            # 获取转换器的统计信息（如果有的话）
            if hasattr(self.jsonl_converter, 'unknown_units') and self.jsonl_converter.unknown_units:
                stats_report.append(f"\n🔍 发现的未知单位:")
                for unit in sorted(self.jsonl_converter.unknown_units):
                    stats_report.append(f"  - {unit}")
                stats_report.append(f"\n💡 提示: 发现 {len(self.jsonl_converter.unknown_units)} 种未知单位，这些单位保持原样未进行转换。")

            if hasattr(self.jsonl_converter, 'parsed_specifications_success') and self.jsonl_converter.parsed_specifications_success:
                stats_report.append(f"\n✅ 成功解析的specification ({len(self.jsonl_converter.parsed_specifications_success)} 个):")
                for spec in sorted(self.jsonl_converter.parsed_specifications_success):
                    stats_report.append(f"  - {spec}")

            if hasattr(self.jsonl_converter, 'parsed_specifications_failed') and self.jsonl_converter.parsed_specifications_failed:
                total_failed_count = sum(self.jsonl_converter.parsed_specifications_failed_count.values())
                stats_report.append(f"\n❌ 解析失败的specification统计:")
                stats_report.append(f"  - 解析失败的specification种类数: {len(self.jsonl_converter.parsed_specifications_failed)} 个")
                stats_report.append(f"  - 解析失败的总次数: {total_failed_count:,} 次")
                stats_report.append(f"  - 平均每个specification失败次数: {total_failed_count/len(self.jsonl_converter.parsed_specifications_failed):.1f} 次")
                stats_report.append(f"\n❌ 解析失败的specification详情 ({len(self.jsonl_converter.parsed_specifications_failed)} 个):")
                sorted_failed = sorted(self.jsonl_converter.parsed_specifications_failed_count.items(), key=lambda x: x[1], reverse=True)
                for spec, count in sorted_failed:
                    if len(spec) > 80:
                        stats_report.append(f"  - {spec[:80]}... (失败 {count:,} 次)")
                    else:
                        stats_report.append(f"  - {spec} (失败 {count:,} 次)")

            # 输出到控制台
            for line in stats_report:
                print(line)

            # 写入统计信息到日志文件
            with open(stats_file_path, 'w', encoding='utf-8') as statsfile:
                statsfile.write(f"转换统计报告 - 生成时间: {datetime.now()}\n")
                statsfile.write("=" * 80 + "\n")
                for line in stats_report:
                    statsfile.write(line + "\n")

            self.logger.info(f"📄 统计报告已保存到: {stats_file_path}")

        except Exception as e:
            self.logger.error(f"生成统计报告失败: {e}")

    def _save_step_errors(self, errors: List, file_path: str, error_type: str):
        """保存步骤错误到文件"""
        if not errors:
            return

        try:
            import json
            from pathlib import Path
            from datetime import datetime

            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            if error_type in ["duplicates"]:
                # 对于去重日志，保存为文本格式
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"去重日志 - 生成时间: {datetime.now()}\n")
                    f.write("=" * 80 + "\n")
                    for error in errors:
                        f.write(f"重复记录: {error}\n")
            elif file_path.endswith(".csv"):
                # 对于CSV格式的错误文件
                if errors and isinstance(errors[0], dict):
                    import pandas as pd
                    pd.DataFrame(errors).to_csv(file_path, index=False)
                else:
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write("error\n")
                        for error in errors:
                            f.write(f"{error}\n")
            else:
                # 对于日志格式的错误文件
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(f"警告日志 - 生成时间: {datetime.now()}\n")
                    f.write("=" * 80 + "\n")
                    for error in errors:
                        if isinstance(error, dict):
                            f.write(f"行号: {error.get('line_number', 'N/A')}\n")
                            f.write(f"错误: {error.get('errors', error.get('warnings', 'N/A'))}\n")
                            f.write("-" * 40 + "\n")
                        else:
                            f.write(f"{error}\n")

            self.logger.info(f"错误日志已保存到: {file_path}")

        except Exception as e:
            self.logger.error(f"保存{error_type}失败: {e}")

    def print_final_summary(self, paths: Dict[str, str]):
        """打印最终摘要"""
        from pathlib import Path

        summary_lines = []
        summary_lines.append("\n📋 处理完成摘要:")
        summary_lines.append(f"  📁 输出目录: {self.config.output_dir}")
        summary_lines.append(f"  📁 临时目录: {self.config.temp_dir}")
        summary_lines.append(f"  📁 日志目录: {self.config.log_dir}")
        summary_lines.append(f"  📄 最终数据文件: {paths['final_csv']}")
        summary_lines.append(f"  📝 重复记录日志: {paths['duplicates_log']}")

        # 统计文件大小
        final_csv_path = Path(paths["final_csv"])
        if final_csv_path.exists():
            size_mb = final_csv_path.stat().st_size / 1024 / 1024
            summary_lines.append(f"  📊 最终文件大小: {size_mb:.1f} MB")

        # 日志文件信息
        summary_lines.append("\n📝 日志文件:")
        log_files = [
            (paths["pipeline_log"], "主要运行日志"),
            (paths["full_execution_log"], "详细执行日志"),
        ]
        
        for log_file, description in log_files:
            log_path = Path(log_file)
            if log_path.exists():
                size_kb = log_path.stat().st_size / 1024
                summary_lines.append(f"  📄 {description}: {log_file} ({size_kb:.1f} KB)")

        # 统计错误文件
        error_files = [
            (paths["step1_errors"], "步骤1转换错误"),
            (paths["step1_warnings"], "步骤1转换警告"),
            (paths["step2_errors"], "步骤2 Brain API错误"),
            (paths["step3_errors"], "步骤3 ClickHouse错误"),
        ]

        has_errors = False
        for error_file, description in error_files:
            error_path = Path(error_file)
            if error_path.exists() and error_path.stat().st_size > 0:
                if not has_errors:
                    summary_lines.append("\n❌ 错误文件:")
                    has_errors = True
                summary_lines.append(f"  📄 {description}: {error_file}")

        # 统计临时文件
        temp_files = [
            (paths["temp_step1_csv"], "步骤1中间结果"),
            (paths["temp_step2_csv"], "步骤2中间结果"),
            (paths["temp_step3_csv"], "步骤3中间结果"),
        ]

        summary_lines.append("\n📂 临时文件:")
        for temp_file, description in temp_files:
            temp_path = Path(temp_file)
            if temp_path.exists():
                size_mb = temp_path.stat().st_size / 1024 / 1024
                summary_lines.append(f"  📄 {description}: {temp_file} ({size_mb:.1f} MB)")
        
        # 输出到控制台和日志
        for line in summary_lines:
            print(line)
            self.logger.info(line.strip())

    def cleanup_temp_files(self):
        """清理临时文件"""
        from pathlib import Path

        paths = self.get_file_paths()
        temp_files = [
            paths["temp_step1_csv"],
            paths["temp_step2_csv"],
            paths["temp_step3_csv"],
        ]

        for temp_file in temp_files:
            temp_path = Path(temp_file)
            if temp_path.exists():
                temp_path.unlink()
                print(f"🗑️ 已删除临时文件: {temp_file}")
                self.logger.info(f"已删除临时文件: {temp_file}")

    async def run_optimized_pipeline(
        self, input_jsonl_file: str
    ) -> Tuple[pd.DataFrame, Dict[str, List]]:
        """运行优化的两遍扫描流水线

        Args:
            input_jsonl_file: 输入的JSONL文件路径

        Returns:
            Tuple[pd.DataFrame, Dict[str, List]]: (最终处理的DataFrame, 各步骤的错误记录)
        """
        if not self.config.enable_optimizations:
            self.logger.warning("优化功能未启用，回退到标准流水线")
            return await self.run_pipeline(input_jsonl_file)

        if not self.enhanced_brain_updater or not self.enhanced_clickhouse_updater:
            self.logger.warning("优化处理器未初始化，回退到标准流水线")
            return await self.run_pipeline(input_jsonl_file)

        self.monitor.start_pipeline()

        # 记录系统信息和配置
        self._log_system_info()
        self.logger.info("🚀 开始优化的两遍扫描流水线")

        self.logger.info(f"开始优化流水线处理: {input_jsonl_file}")

        # 检查输入文件
        from pathlib import Path
        input_path = Path(input_jsonl_file)
        if not input_path.exists():
            self.logger.error(f"输入文件不存在: {input_jsonl_file}")
            raise FileNotFoundError(f"输入文件不存在: {input_jsonl_file}")

        file_size_mb = input_path.stat().st_size / 1024 / 1024
        self.logger.info(f"输入文件大小: {file_size_mb:.1f} MB")

        # 获取文件路径
        paths = self.get_file_paths()

        all_errors = {
            "jsonl_conversion": [],
            "brain_api_update": [],
            "clickhouse_update": [],
            "deduplication": [],
        }

        try:
            # 步骤1: JSONL转换为DataFrame（与标准流水线相同）
            self.monitor.start_step("jsonl_conversion")
            self.logger.info("步骤1: 转换JSONL文件为DataFrame")
            self.logger.info(f"正在处理文件: {input_jsonl_file}")

            success_df, error_records, warning_records = self._capture_and_log_output(
                self.jsonl_converter.convert_jsonl_to_dataframe, input_jsonl_file, str(self.config.temp_dir)
            )
            all_errors["jsonl_conversion"].extend(error_records)
            all_errors["jsonl_conversion"].extend(warning_records)

            # 生成详细的统计报告
            self.logger.info("生成JSONL转换统计报告...")
            self._generate_conversion_stats_report(
                success_df, error_records, warning_records, paths["step1_stats"]
            )
            self.logger.info("统计报告生成完成")

            # 保存步骤1的中间结果和错误日志
            if not success_df.empty:
                self.logger.info("保存步骤1中间结果...")
                success_df.to_csv(paths["temp_step1_csv"], index=False)
                self.logger.info(f"步骤1中间结果已保存到: {paths['temp_step1_csv']}")

            self.logger.info("保存步骤1错误和警告日志...")
            self._save_step_errors(
                error_records, paths["step1_errors"], "conversion_errors"
            )
            self._save_step_errors(
                warning_records, paths["step1_warnings"], "conversion_warnings"
            )
            self.logger.info("步骤1日志保存完成")

            self.monitor.end_step(
                "jsonl_conversion",
                len(success_df),
                len(error_records) + len(warning_records),
            )
            self.logger.info(
                f"JSONL转换完成: 成功 {len(success_df)} 行, 错误/警告 {len(error_records) + len(warning_records)} 条"
            )

            if success_df.empty:
                self.logger.warning("JSONL转换后没有有效数据，流水线终止")
                return success_df, all_errors

            # 步骤2: 优化的Brain API更新SMILES
            self.monitor.start_step("brain_api_update")
            self.logger.info("步骤2: 使用优化的Brain API更新SMILES")
            self.logger.info(f"待更新数据行数: {len(success_df)}")

            self.logger.info("开始优化的Brain API更新...")
            updated_df, brain_errors = await self.enhanced_brain_updater.update_dataframe(
                success_df
            )
            self.logger.info("优化的Brain API更新完成")
            all_errors["brain_api_update"].extend(brain_errors)

            # 记录优化统计信息
            brain_stats = self.enhanced_brain_updater.get_stats()
            self.logger.info(f"Brain API优化统计: {brain_stats}")

            # 保存步骤2的中间结果和错误日志
            if not updated_df.empty:
                self.logger.info("保存步骤2中间结果...")
                updated_df.to_csv(paths["temp_step2_csv"], index=False)
                self.logger.info(f"步骤2中间结果已保存到: {paths['temp_step2_csv']}")

            self.logger.info("保存步骤2错误日志...")
            self._save_step_errors(brain_errors, paths["step2_errors"], "brain_errors")
            self.logger.info("步骤2日志保存完成")

            self.monitor.end_step(
                "brain_api_update", len(updated_df), len(brain_errors)
            )
            self.logger.info(
                f"优化的Brain API更新完成: 成功 {len(updated_df)} 行, 错误 {len(brain_errors)} 条"
            )

            # 步骤3: 优化的ClickHouse更新hazard信息
            self.monitor.start_step("clickhouse_update")
            self.logger.info("步骤3: 使用优化的ClickHouse更新hazard信息")
            self.logger.info(f"待更新数据行数: {len(updated_df)}")

            hazard_updated_df, clickhouse_errors = self.enhanced_clickhouse_updater.update_dataframe(updated_df)
            all_errors["clickhouse_update"].extend(clickhouse_errors)

            # 记录优化统计信息
            clickhouse_stats = self.enhanced_clickhouse_updater.get_stats()
            self.logger.info(f"ClickHouse优化统计: {clickhouse_stats}")

            # 保存步骤3的中间结果和错误日志
            if not hazard_updated_df.empty:
                self.logger.info("保存步骤3中间结果...")
                hazard_updated_df.to_csv(paths["temp_step3_csv"], index=False)
                self.logger.info(f"步骤3中间结果已保存到: {paths['temp_step3_csv']}")

            self.logger.info("保存步骤3错误日志...")
            self._save_step_errors(
                clickhouse_errors, paths["step3_errors"], "clickhouse_errors"
            )
            self.logger.info("步骤3日志保存完成")

            self.monitor.end_step(
                "clickhouse_update", len(hazard_updated_df), len(clickhouse_errors)
            )
            self.logger.info(
                f"优化的ClickHouse更新完成: 成功 {len(hazard_updated_df)} 行, 错误 {len(clickhouse_errors)} 条"
            )

            # 步骤4: 去重和价格更新（与标准流水线相同）
            self.monitor.start_step("deduplication")
            self.logger.info("步骤4: 去重和价格更新")
            self.logger.info(f"去重前数据行数: {len(hazard_updated_df)}")

            final_df, dedup_records = self._capture_and_log_output(
                deduplicate_and_update_dataframe, hazard_updated_df
            )
            all_errors["deduplication"].extend(dedup_records)

            # 保存最终结果
            if not final_df.empty:
                self.logger.info("保存最终处理结果...")
                final_df.to_csv(paths["final_csv"], index=False)
                self.logger.info(f"最终结果已保存到: {paths['final_csv']}")

            # 保存去重日志
            self.logger.info("保存去重日志...")
            self._save_step_errors(dedup_records, paths["duplicates_log"], "duplicates")
            self.logger.info("步骤4日志保存完成")

            self.monitor.end_step("deduplication", len(final_df), len(dedup_records))
            self.logger.info(
                f"去重和价格更新完成: 最终 {len(final_df)} 行, 记录 {len(dedup_records)} 条"
            )

            return final_df, all_errors

        except Exception as e:
            self.logger.error(f"优化流水线执行出错: {e}")
            self.logger.exception("详细错误信息:")
            raise
        finally:
            self.monitor.end_pipeline()
            self.monitor.print_summary()
            self.print_final_summary(paths)

            # 记录最终统计信息
            self.logger.info("\n" + "=" * 80)
            self.logger.info("优化流水线执行完成")
            self.logger.info(f"总执行时间: {self.monitor.stats.total_duration:.2f} 秒")
            self.logger.info(f"最终内存使用: {self.monitor.stats.memory_usage.get('pipeline_end', 0):.1f} MB")
            self.logger.info("所有日志已保存到文件")
            self.logger.info("=" * 80)

    async def save_results(
        self, df: pd.DataFrame, errors: Dict[str, List], output_dir: str
    ):
        """保存处理结果

        Args:
            df: 最终处理的DataFrame
            errors: 各步骤的错误记录
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存最终结果
        output_file = os.path.join(output_dir, "final_processed_data.csv")
        df.to_csv(output_file, index=False)
        self.logger.info(f"最终结果已保存到: {output_file}")

        # 保存各步骤的错误记录
        for step, error_list in errors.items():
            if error_list:
                error_file = os.path.join(output_dir, f"{step}_errors.json")
                import json

                with open(error_file, "w", encoding="utf-8") as f:
                    json.dump(error_list, f, ensure_ascii=False, indent=2)
                self.logger.info(f"{step}错误记录已保存到: {error_file}")


async def main():
    """主函数"""
    import argparse
    from pathlib import Path

    parser = argparse.ArgumentParser(
        description="模块化数据处理流水线",
        formatter_class=argparse.RawTextHelpFormatter,
    )

    parser.add_argument("input_file", help="输入JSONL文件路径")

    parser.add_argument(
        "--output-dir", default="./output", help="输出目录 (默认: ./output)"
    )

    parser.add_argument(
        "--temp-dir", default="./temp", help="临时文件目录 (默认: ./temp)"
    )

    parser.add_argument("--config-file", help="配置文件路径 (可选)")

    parser.add_argument(
        "--brain-api-batch-size",
        type=int,
        default=10000,
        help="Brain API批处理大小 (默认: 10000)",
    )

    parser.add_argument(
        "--clickhouse-host",
        default="localhost",
        help="ClickHouse主机地址 (默认: localhost)",
    )

    parser.add_argument(
        "--cleanup-temp", action="store_true", help="完成后清理临时文件"
    )

    parser.add_argument(
        "--step-timeout", type=int, default=3600, help="单步骤超时时间(秒) (默认: 3600)"
    )

    parser.add_argument(
        "--optimized", action="store_true", help="使用优化的两遍扫描流水线"
    )

    args = parser.parse_args()

    # 先创建基础配置
    config = PipelineConfig(input_file=Path(args.input_file))

    # 如果提供了配置文件，先加载配置文件
    if args.config_file:
        config.load_from_file(args.config_file)

    # 然后用命令行参数覆盖配置（只覆盖非默认值）
    if args.output_dir != "./output":
        config.output_dir = Path(args.output_dir)
    if args.temp_dir != "./temp":
        config.temp_dir = Path(args.temp_dir)
    if args.brain_api_batch_size != 10000:
        config.brain_api_batch_size = args.brain_api_batch_size
    if args.clickhouse_host != "localhost":
        config.clickhouse_host = args.clickhouse_host
    if args.cleanup_temp:
        config.cleanup_temp = args.cleanup_temp
    if args.step_timeout != 3600:
        config.step_timeout = args.step_timeout

    # 如果使用优化模式，启用优化功能
    if args.optimized:
        config.enable_optimizations = True
        print("✅ 优化模式已启用")

    # 验证配置
    print("🔍 验证配置...")
    if not config.validate():
        print("❌ 配置验证失败，流水线终止")
        return 1
    print("✅ 配置验证通过")

    # 创建并运行流水线
    print("🚀 初始化模块化数据处理流水线...")
    pipeline = ModularPipeline(config)
    
    print(f"📂 日志文件位置:")
    print(f"  主要日志: {config.log_dir / 'modular_pipeline.log'}")
    print(f"  详细日志: {config.log_dir / 'full_execution.log'}")
    print("\n🔄 开始执行流水线...")

    try:
        # 选择运行模式
        if args.optimized:
            print("🚀 使用优化的两遍扫描流水线...")
            final_df, errors = await pipeline.run_optimized_pipeline(args.input_file)
        else:
            print("🚀 使用标准流水线...")
            final_df, errors = await pipeline.run_pipeline(args.input_file)

        # 清理临时文件（如果配置允许）
        if config.cleanup_temp:
            print("🧹 清理临时文件...")
            pipeline.cleanup_temp_files()

        print(f"\n🎉 流水线执行完成! 最终处理了 {len(final_df)} 行数据")
        print(f"📁 结果已保存到: {config.output_dir}")
        print(f"📝 详细日志已保存到: {config.log_dir}")

    except Exception as e:
        print(f"💥 流水线执行失败: {e}")
        # 确保错误也记录到日志中
        if 'pipeline' in locals():
            pipeline.logger.error(f"流水线执行失败: {e}")
            pipeline.logger.exception("详细错误信息:")
        return 1

    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))
