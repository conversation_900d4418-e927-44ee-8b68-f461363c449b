#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从CSV文件中提取指定列到新的CSV文件

此脚本从输入的CSV文件中提取指定的列，并将结果保存到新的CSV文件中。
提取的列包括：id, inchified_smiles, unit_price, material_lib_id, material_id, price, unit, codes

用法:
    python3 extract_columns.py input.csv output.csv
"""

import pandas as pd
import argparse
import os
import sys

def extract_columns(input_file: str, output_file: str):
    """
    从输入CSV文件中提取指定列并保存到输出文件
    
    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
    """
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在: {input_file}")
        sys.exit(1)
    
    print(f"正在读取数据: {input_file}...")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        
        # 定义需要提取的列
        required_columns = [
            'id', 
            'inchified_smiles', 
            'unit_price', 
            'material_lib_id', 
            'material_id', 
            'price', 
            'unit', 
            'codes'
        ]
        
        # 检查所有必需的列是否存在
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"错误: 输入文件中缺少以下列: {missing_columns}")
            print(f"可用的列: {list(df.columns)}")
            sys.exit(1)
        
        # 提取指定列
        extracted_df = df[required_columns].copy()
        
        print(f"成功提取 {len(extracted_df)} 行数据，包含 {len(required_columns)} 列")
        
        # 创建输出目录（如果不存在）
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 保存到输出文件
        extracted_df.to_csv(output_file, index=False)
        print(f"数据已保存到: {output_file}")
        
        # 显示数据预览
        print("\n数据预览:")
        print(extracted_df.head())
        
    except Exception as e:
        print(f"处理文件时发生错误: {str(e)}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description="从CSV文件中提取指定列到新的CSV文件",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("input_file", help="输入CSV文件路径")
    parser.add_argument("output_file", nargs='?', default=None, 
                       help="输出CSV文件路径 (可选). 默认为 <input>_extracted.csv")
    
    args = parser.parse_args()
    
    # 如果没有指定输出文件，则生成默认名称
    if args.output_file is None:
        input_dir, input_filename = os.path.split(args.input_file)
        input_name, input_ext = os.path.splitext(input_filename)
        args.output_file = os.path.join(input_dir, f"{input_name}_extracted{input_ext}")
    
    extract_columns(args.input_file, args.output_file)

if __name__ == "__main__":
    main()