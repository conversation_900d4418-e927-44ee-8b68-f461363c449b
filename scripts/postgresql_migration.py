#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL数据库迁移脚本

执行以下步骤：
1. 备份material_items和material_libs表的数据
2. 更新material_libs表
3. 更新material_items表（从CSV导入）
4. 更新material_items_material_lib_links关联表

作者: Assistant
日期: 2024
"""

import os
import sys
import logging
import argparse
import csv
import tempfile
import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv


class PostgreSQLMigrator:
    """PostgreSQL数据库迁移器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.conn = None
        
        # 供应商映射
        self.vendors = [
            {'id': 1, 'name': '<PERSON>yan', 'description': '乐研' },
            {'id': 2, 'name': '<PERSON><PERSON>', 'description': '麦克林' },
            {'id': 3, 'name': 'BidePharm', 'description': '百灵威' },
            {'id': 4, 'name': 'Aladdin', 'description': '阿拉丁' },
            {'id': 5, 'name': 'Merck', 'description': '默克' },
            {'id': 6, 'name': 'Rhawn', 'description': '罗恩' },
            {'id': 7, 'name': 'TCI', 'description': '东京化成' },
            {'id': 8, 'name': 'Reagent', 'description': '试剂' },
        ]
    
    def connect(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(
                host=self.config['DB_HOST'],
                port=self.config['DB_PORT'],
                database=self.config['DB_NAME'],
                user=self.config['DB_USER'],
                password=self.config['DB_PASSWORD']
            )
            self.conn.autocommit = False
            self.logger.info("数据库连接成功")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def disconnect(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
            self.logger.info("数据库连接已关闭")
    
    def ensure_connection(self):
        """确保数据库连接有效，如果连接断开则重新连接"""
        try:
            if not hasattr(self, 'conn') or not self.conn or self.conn.closed:
                self.logger.warning("检测到数据库连接已断开，正在重新连接...")
                self.connect()
            else:
                # 测试连接是否有效
                with self.conn.cursor() as cur:
                    cur.execute("SELECT 1;")
        except Exception as e:
            self.logger.warning(f"连接测试失败: {e}，正在重新连接...")
            try:
                self.connect()
            except Exception as reconnect_error:
                self.logger.error(f"重新连接失败: {reconnect_error}")
                raise
    
    def backup_tables(self):
        """备份现有表数据到本地文件"""
        self.logger.info("开始备份表数据到本地文件...")
        
        # 创建备份目录
        if 'BACKUP_DIR' in self.config and self.config['BACKUP_DIR']:
            backup_base = Path(self.config['BACKUP_DIR'])
        else:
            backup_base = Path('.')
        
        backup_dir = backup_base / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        try:
            with self.conn.cursor() as cur:
                # 备份material_items表
                self.logger.info("备份material_items表...")
                items_backup_file = backup_dir / "material_items_backup.csv"
                with open(items_backup_file, 'w', encoding='utf-8') as f:
                    cur.copy_expert(
                        "COPY material_items TO STDOUT WITH CSV HEADER DELIMITER ','", 
                        f
                    )
                self.logger.info(f"material_items表已备份到: {items_backup_file}")
                
                # 备份material_libs表
                self.logger.info("备份material_libs表...")
                libs_backup_file = backup_dir / "material_libs_backup.csv"
                with open(libs_backup_file, 'w', encoding='utf-8') as f:
                    cur.copy_expert(
                        "COPY material_libs TO STDOUT WITH CSV HEADER DELIMITER ','", 
                        f
                    )
                self.logger.info(f"material_libs表已备份到: {libs_backup_file}")
                
                self.logger.info(f"表备份完成，备份文件保存在: {backup_dir.absolute()}")
                
        except Exception as e:
            self.logger.error(f"备份失败: {e}")
            raise
    
    def update_material_libs(self):
        """更新material_libs表"""
        self.logger.info("开始更新material_libs表...")
        
        try:
            with self.conn.cursor() as cur:
                # 清空表并重置序列
                cur.execute("TRUNCATE TABLE material_libs RESTART IDENTITY CASCADE;")
                
                # 插入新的供应商数据
                insert_sql = """
                    INSERT INTO material_libs (id, name, version, description, status, 
                                             last_update_time, created_at, updated_at) 
                    VALUES %s
                """
                
                values = [
                    (vendor['id'], vendor['name'], datetime.now().strftime('%Y-%m-%d'), vendor['description'], 
                     'active', datetime.now(), datetime.now(), datetime.now())
                    for vendor in self.vendors
                ]
                
                execute_values(cur, insert_sql, values)
                
                # 重置序列
                cur.execute("SELECT setval('material_libs_id_seq', 8);")
                
                self.conn.commit()
                self.logger.info("material_libs表更新完成")
                
        except Exception as e:
            self.conn.rollback()
            self.logger.error(f"更新material_libs表失败: {e}")
            raise
    
    def estimate_import_time(self, csv_file_path: str) -> tuple:
        """
        预估导入时间
        """
        import os
        
        file_size_mb = os.path.getsize(csv_file_path) / 1024 / 1024
        
        # 基于经验值估算（每MB约需要0.5-2秒）
        min_time = file_size_mb * 0.5
        max_time = file_size_mb * 2
        
        self.logger.info(f"文件大小: {file_size_mb:.2f}MB")
        self.logger.info(f"预估导入时间: {min_time:.1f}-{max_time:.1f}秒")
        
        return min_time, max_time
    
    def optimize_database_for_import(self):
        """
        导入前数据库优化
        """
        self.logger.info("开始数据库导入优化...")
        
        try:
            with self.conn.cursor() as cur:
                # 保存当前设置
                cur.execute("SHOW work_mem;")
                original_work_mem = cur.fetchone()[0]
                
                cur.execute("SHOW maintenance_work_mem;")
                original_maintenance_work_mem = cur.fetchone()[0]
                
                cur.execute("SHOW synchronous_commit;")
                original_sync_commit = cur.fetchone()[0]
                
                # 存储原始设置
                self.original_settings = {
                    'work_mem': original_work_mem,
                    'maintenance_work_mem': original_maintenance_work_mem,
                    'synchronous_commit': original_sync_commit
                }
                
                # 应用优化设置（移除无法动态修改的参数）
                optimizations = [
                    "SET work_mem = '256MB';",
                    "SET maintenance_work_mem = '1GB';",
                    "SET synchronous_commit = off;"
                ]
                
                for opt in optimizations:
                    cur.execute(opt)
                    self.logger.info(f"应用优化: {opt}")
                
                self.conn.commit()
                self.logger.info("数据库导入优化完成")
                
        except Exception as e:
            self.logger.error(f"数据库优化失败: {e}")
            raise
    
    def restore_database_settings(self):
        """
        恢复数据库原始设置
        """
        if not hasattr(self, 'original_settings'):
            return
        
        # 检查连接状态
        if not hasattr(self, 'conn') or not self.conn or self.conn.closed:
            self.logger.warning("数据库连接已关闭，无法恢复设置")
            return
            
        self.logger.info("恢复数据库原始设置...")
        
        try:
            with self.conn.cursor() as cur:
                # 恢复原始设置
                cur.execute(f"SET work_mem = '{self.original_settings['work_mem']}';")
                cur.execute(f"SET maintenance_work_mem = '{self.original_settings['maintenance_work_mem']}';")
                cur.execute(f"SET synchronous_commit = {self.original_settings['synchronous_commit']};")
                
                self.conn.commit()
                self.logger.info("数据库设置已恢复")
                
        except Exception as e:
            self.logger.error(f"恢复数据库设置失败: {e}")
    
    def drop_indexes_for_import(self):
        """
        导入前删除索引以提高性能
        """
        self.logger.info("删除索引以优化导入性能...")
        
        try:
            with self.conn.cursor() as cur:
                # 查询现有索引
                cur.execute("""
                    SELECT indexname, tablename 
                    FROM pg_indexes 
                    WHERE tablename = 'material_items' 
                    AND indexname != 'material_items_pkey';
                """)
                
                indexes = cur.fetchall()
                self.dropped_indexes = []
                
                # 删除非主键索引
                for index_name, table_name in indexes:
                    try:
                        cur.execute(f"DROP INDEX IF EXISTS {index_name};")
                        self.dropped_indexes.append(index_name)
                        self.logger.info(f"删除索引: {index_name}")
                    except Exception as e:
                        self.logger.warning(f"删除索引 {index_name} 失败: {e}")
                
                self.conn.commit()
                self.logger.info(f"成功删除 {len(self.dropped_indexes)} 个索引")
                
        except Exception as e:
            self.logger.error(f"删除索引失败: {e}")
            raise
    
    def recreate_indexes_after_import(self):
        """
        导入后重建索引
        """
        if not hasattr(self, 'dropped_indexes') or not self.dropped_indexes:
            return
            
        self.logger.info("重建索引...")
        
        try:
            with self.conn.cursor() as cur:
                # 重建常用索引
                index_definitions = {
                    'idx_material_items_cas_no': 'CREATE INDEX CONCURRENTLY idx_material_items_cas_no ON material_items(cas_no);',
                    'idx_material_items_source': 'CREATE INDEX CONCURRENTLY idx_material_items_source ON material_items(source);',
                    'idx_material_items_material_lib_id': 'CREATE INDEX CONCURRENTLY idx_material_items_material_lib_id ON material_items(material_lib_id);',
                    'idx_material_items_canonical_smiles': 'CREATE INDEX CONCURRENTLY idx_material_items_canonical_smiles ON material_items(canonical_smiles);'
                }
                
                for index_name in self.dropped_indexes:
                    if index_name in index_definitions:
                        try:
                            cur.execute(index_definitions[index_name])
                            self.logger.info(f"重建索引: {index_name}")
                        except Exception as e:
                            self.logger.warning(f"重建索引 {index_name} 失败: {e}")
                
                self.conn.commit()
                self.logger.info("索引重建完成")
                
        except Exception as e:
            self.logger.error(f"重建索引失败: {e}")
    
    def update_material_items(self, csv_file_path: str):
        """
        更新material_items表数据 - 使用COPY FROM STDIN方式（带进度提示）
        """
        import os
        import time
        import csv
        from io import StringIO
        
        try:
            # 预估导入时间和文件信息
            min_time, max_time = self.estimate_import_time(csv_file_path)
            file_size = os.path.getsize(csv_file_path)
            
            # 应用数据库优化
            self.optimize_database_for_import()
            
            # 删除索引以提高导入性能
            self.drop_indexes_for_import()
            
            with self.conn.cursor() as cur:
                # 清空表
                cur.execute("TRUNCATE TABLE material_items RESTART IDENTITY CASCADE;")
                self.conn.commit()
                self.logger.info("已清空material_items表")
            
            # 开始导入过程
            self.logger.info(f"开始使用COPY FROM STDIN导入数据: {csv_file_path}")
            self.logger.info(f"文件大小: {file_size / 1024 / 1024:.2f}MB")
            self.logger.info(f"预估导入时间: {min_time:.1f}-{max_time:.1f}秒")
            
            start_time = time.time()
            total_rows = 0
            processed_bytes = 0
            last_progress_time = start_time
            
            # 定义列名
            columns = [
                'id', 'material_lib_id', 'source', 'source_link', 'canonical_smiles', 
                'inchified_smiles', 'unified_smiles', 'cas_no', 'codes', 'pubchem_safety_link', 
                'name_en', 'name_zh', 'material_id', 'quantity', 'unit', 'price', 
                'unified_unit', 'unit_price', 'unit_quantity', 'lowest_unit_price', 
                'in_stock', 'max_delivery_days', 'min_delivery_days', 'purity', 'extension'
            ]
            
            # 预处理CSV文件，清理JSON格式问题
            cleaned_csv_path = self._preprocess_csv_for_json(csv_file_path, columns)
            
            try:
                with self.conn.cursor() as cur:
                    # 使用COPY FROM STDIN
                    copy_sql = f"COPY material_items ({', '.join(columns)}) FROM STDIN WITH CSV HEADER DELIMITER ','"
                    
                    with open(cleaned_csv_path, 'r', encoding='utf-8') as csvfile:
                        # 开始COPY操作
                        cur.copy_expert(copy_sql, csvfile)
                        
                        # 由于copy_expert是一次性操作，我们需要用另一种方式显示进度
                        # 先统计总行数用于进度显示
                        csvfile.seek(0)
                        total_lines = sum(1 for _ in csvfile) - 1  # 减去标题行
                
                # 提交COPY操作
                self.conn.commit()
                
                # 获取实际导入的记录数和重置序列（使用新的cursor）
                with self.conn.cursor() as cur:
                    cur.execute("SELECT COUNT(*) FROM material_items;")
                    imported_count = cur.fetchone()[0]
                    
                    actual_time = time.time() - start_time
                    
                    self.logger.info(f"✅ 导入完成!")
                    self.logger.info(f"📊 成功导入 {imported_count:,} 条记录到material_items表")
                    self.logger.info(f"⏱️  实际导入时间: {actual_time:.1f}秒 (预估: {min_time:.1f}-{max_time:.1f}秒)")
                    self.logger.info(f"🚀 导入速度: {imported_count/actual_time:.0f} 记录/秒")
                    
                    # 重置序列
                    cur.execute("""
                        SELECT setval('material_items_id_seq', 
                                      (SELECT MAX(id) FROM material_items));
                    """)
                    
                    self.conn.commit()
                    self.logger.info("material_items表更新完成")
                    
            finally:
                # 清理临时文件
                if cleaned_csv_path != csv_file_path and os.path.exists(cleaned_csv_path):
                    os.remove(cleaned_csv_path)
            
            # 重建索引
            self.recreate_indexes_after_import()
            
            # 恢复数据库设置
            self.restore_database_settings()
                
        except Exception as e:
            # 检查连接状态并安全地回滚
            if hasattr(self, 'conn') and self.conn and not self.conn.closed:
                try:
                    self.conn.rollback()
                except Exception as rollback_error:
                    self.logger.warning(f"回滚失败: {rollback_error}")
            
            self.logger.error(f"更新material_items表失败: {str(e)}")
            
            # 确保在失败时也恢复设置（如果连接仍然有效）
            try:
                self.restore_database_settings()
            except Exception as restore_error:
                self.logger.warning(f"恢复数据库设置失败: {restore_error}")
            
            raise
    
    def update_material_items_with_progress(self, csv_file_path: str, batch_size: int = 10000):
        """
        更新material_items表数据 - 分批处理版本（实时进度显示）
        """
        import os
        import time
        import csv
        from io import StringIO
        
        try:
            # 预估导入时间和文件信息
            min_time, max_time = self.estimate_import_time(csv_file_path)
            file_size = os.path.getsize(csv_file_path)
            
            # 应用数据库优化
            self.optimize_database_for_import()
            
            # 删除索引以提高导入性能
            self.drop_indexes_for_import()
            
            with self.conn.cursor() as cur:
                # 清空表
                cur.execute("TRUNCATE TABLE material_items RESTART IDENTITY CASCADE;")
                self.conn.commit()
                self.logger.info("已清空material_items表")
            
            # 统计总行数
            self.logger.info("📊 正在统计文件行数...")
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                total_lines = sum(1 for _ in f) - 1  # 减去标题行
            
            self.logger.info(f"📁 文件信息: {file_size / 1024 / 1024:.2f}MB, {total_lines:,} 行数据")
            self.logger.info(f"⏱️  预估导入时间: {min_time:.1f}-{max_time:.1f}秒")
            self.logger.info(f"🔄 开始分批导入 (批次大小: {batch_size:,})")
            
            start_time = time.time()
            processed_rows = 0
            batch_count = 0
            
            # 定义列名
            columns = [
                'id', 'material_lib_id', 'source', 'source_link', 'canonical_smiles', 
                'inchified_smiles', 'unified_smiles', 'cas_no', 'codes', 'pubchem_safety_link', 
                'name_en', 'name_zh', 'material_id', 'quantity', 'unit', 'price', 
                'unified_unit', 'unit_price', 'unit_quantity', 'lowest_unit_price', 
                'in_stock', 'max_delivery_days', 'min_delivery_days', 'purity', 'extension'
            ]
            
            with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
                csv_reader = csv.reader(csvfile)
                header = next(csv_reader)  # 跳过标题行
                
                batch_data = []
                
                for row in csv_reader:
                    batch_data.append(row)
                    
                    if len(batch_data) >= batch_size:
                        # 确保连接有效（每10个批次检查一次）
                        if batch_count % 10 == 0:
                            self.ensure_connection()
                        
                        # 处理当前批次
                        self._process_batch(batch_data, columns, batch_count + 1)
                        processed_rows += len(batch_data)
                        batch_count += 1
                        
                        # 显示进度
                        progress = (processed_rows / total_lines) * 100
                        elapsed_time = time.time() - start_time
                        estimated_total_time = elapsed_time / (processed_rows / total_lines) if processed_rows > 0 else 0
                        remaining_time = estimated_total_time - elapsed_time
                        
                        self.logger.info(
                            f"📈 进度: {progress:.1f}% ({processed_rows:,}/{total_lines:,}) | "
                            f"⏱️  已用时: {elapsed_time:.1f}s | 预计剩余: {remaining_time:.1f}s | "
                            f"🚀 速度: {processed_rows/elapsed_time:.0f} 记录/秒"
                        )
                        
                        batch_data = []
                
                # 处理最后一批数据
                if batch_data:
                    self._process_batch(batch_data, columns, batch_count + 1)
                    processed_rows += len(batch_data)
            
            actual_time = time.time() - start_time
            
            # 获取最终导入的记录数
            with self.conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM material_items;")
                final_count = cur.fetchone()[0]
                
                self.logger.info(f"✅ 导入完成!")
                self.logger.info(f"📊 成功导入 {final_count:,} 条记录到material_items表")
                self.logger.info(f"⏱️  实际导入时间: {actual_time:.1f}秒 (预估: {min_time:.1f}-{max_time:.1f}秒)")
                self.logger.info(f"🚀 平均导入速度: {final_count/actual_time:.0f} 记录/秒")
                
                # 重置序列
                cur.execute("""
                    SELECT setval('material_items_id_seq', 
                                  (SELECT MAX(id) FROM material_items));
                """)
                
                self.conn.commit()
                self.logger.info("material_items表更新完成")
            
            # 重建索引
            self.recreate_indexes_after_import()
            
            # 恢复数据库设置
            self.restore_database_settings()
                
        except Exception as e:
            if hasattr(self, 'conn'):
                self.conn.rollback()
            self.logger.error(f"更新material_items表失败: {str(e)}")
            # 确保在失败时也恢复设置
            self.restore_database_settings()
            raise
    
    def _process_batch(self, batch_data, columns, batch_num):
        """
        处理单个批次的数据
        """
        from io import StringIO
        import json
        import re
        
        # 数据清理和格式转换
        cleaned_batch = []
        codes_index = columns.index('codes') if 'codes' in columns else -1
        extension_index = columns.index('extension') if 'extension' in columns else -1
        
        for row in batch_data:
            cleaned_row = list(row)
            
            # 处理codes字段：将Python列表格式转换为JSON数组
            if codes_index >= 0 and codes_index < len(cleaned_row):
                codes_value = cleaned_row[codes_index]
                if codes_value and isinstance(codes_value, str):
                    # 检查是否是Python列表格式
                    if codes_value.startswith('[') and codes_value.endswith(']'):
                        try:
                            # 使用正则表达式将Python列表转换为JSON格式
                            # 将单引号替换为双引号
                            json_str = re.sub(r"'", '"', codes_value)
                            # 验证JSON格式
                            json.loads(json_str)
                            cleaned_row[codes_index] = json_str
                        except (json.JSONDecodeError, ValueError):
                            # 如果转换失败，设置为空JSON数组
                            cleaned_row[codes_index] = '[]'
                    elif not codes_value.strip():
                        # 空值设置为空JSON数组
                        cleaned_row[codes_index] = '[]'
            
            # 处理extension字段：确保是有效的JSON
            if extension_index >= 0 and extension_index < len(cleaned_row):
                extension_value = cleaned_row[extension_index]
                if extension_value and isinstance(extension_value, str):
                    if not extension_value.strip():
                        cleaned_row[extension_index] = '{}'
                    else:
                        try:
                            # 验证JSON格式
                            json.loads(extension_value)
                        except json.JSONDecodeError:
                            # 如果不是有效JSON，设置为空对象
                            cleaned_row[extension_index] = '{}'
                else:
                    cleaned_row[extension_index] = '{}'
            
            cleaned_batch.append(cleaned_row)
        
        # 将批次数据转换为CSV格式
        output = StringIO()
        csv_writer = csv.writer(output)
        csv_writer.writerows(cleaned_batch)
        output.seek(0)
        
        try:
            # 确保数据库连接有效
            self.ensure_connection()
                
            with self.conn.cursor() as cur:
                copy_sql = f"COPY material_items ({', '.join(columns)}) FROM STDIN WITH CSV DELIMITER ','"
                cur.copy_expert(copy_sql, output)
                self.conn.commit()
        except Exception as e:
            self.logger.error(f"批次 {batch_num} 处理失败: {e}")
            # 检查连接状态并安全地回滚
            if hasattr(self, 'conn') and self.conn and not self.conn.closed:
                try:
                    self.conn.rollback()
                except Exception as rollback_error:
                    self.logger.warning(f"批次 {batch_num} 回滚失败: {rollback_error}")
            raise
    
    def _preprocess_csv_for_json(self, csv_file_path: str, columns: list) -> str:
        """
        预处理CSV文件，清理JSON格式问题
        返回清理后的CSV文件路径
        """
        import tempfile
        import json
        import re
        
        codes_index = columns.index('codes') if 'codes' in columns else -1
        extension_index = columns.index('extension') if 'extension' in columns else -1
        
        # 如果没有需要处理的JSON字段，直接返回原文件
        if codes_index == -1 and extension_index == -1:
            return csv_file_path
        
        # 创建临时文件
        temp_fd, temp_path = tempfile.mkstemp(suffix='.csv', prefix='cleaned_')
        
        try:
            with os.fdopen(temp_fd, 'w', encoding='utf-8', newline='') as temp_file:
                csv_writer = csv.writer(temp_file)
                
                with open(csv_file_path, 'r', encoding='utf-8') as source_file:
                    csv_reader = csv.reader(source_file)
                    
                    # 写入标题行
                    header = next(csv_reader)
                    csv_writer.writerow(header)
                    
                    # 处理数据行
                    for row in csv_reader:
                        cleaned_row = list(row)
                        
                        # 处理codes字段
                        if codes_index >= 0 and codes_index < len(cleaned_row):
                            codes_value = cleaned_row[codes_index]
                            if codes_value and isinstance(codes_value, str):
                                if codes_value.startswith('[') and codes_value.endswith(']'):
                                    try:
                                        # 将Python列表格式转换为JSON格式
                                        json_str = re.sub(r"'", '"', codes_value)
                                        json.loads(json_str)  # 验证JSON格式
                                        cleaned_row[codes_index] = json_str
                                    except (json.JSONDecodeError, ValueError):
                                        cleaned_row[codes_index] = '[]'
                                elif not codes_value.strip():
                                    cleaned_row[codes_index] = '[]'
                        
                        # 处理extension字段
                        if extension_index >= 0 and extension_index < len(cleaned_row):
                            extension_value = cleaned_row[extension_index]
                            if extension_value and isinstance(extension_value, str):
                                if not extension_value.strip():
                                    cleaned_row[extension_index] = '{}'
                                else:
                                    try:
                                        json.loads(extension_value)
                                    except json.JSONDecodeError:
                                        cleaned_row[extension_index] = '{}'
                            else:
                                cleaned_row[extension_index] = '{}'
                        
                        csv_writer.writerow(cleaned_row)
            
            return temp_path
            
        except Exception as e:
            # 如果处理失败，清理临时文件并返回原文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
            self.logger.warning(f"CSV预处理失败，使用原文件: {e}")
            return csv_file_path
    
    def update_material_links(self):
        """更新material_items_material_lib_links关联表"""
        self.logger.info("开始更新material_items_material_lib_links表...")
        
        try:
            with self.conn.cursor() as cur:
                # 清空关联表
                cur.execute("TRUNCATE TABLE material_items_material_lib_links RESTART IDENTITY;")
                
                # 建立关联关系
                cur.execute("""
                    INSERT INTO material_items_material_lib_links 
                    (material_item_id, material_lib_id, material_item_order)
                    SELECT 
                        mi.id as material_item_id,
                        ml.id as material_lib_id,
                        mi.id::float8 as material_item_order
                    FROM material_items mi
                    JOIN material_libs ml ON (
                        CASE 
                            WHEN mi.source = 'Leyan' THEN ml.name = 'Leyan'
                            WHEN mi.source = 'Macklin' THEN ml.name = 'Macklin'
                            WHEN mi.source = 'BidePharm' THEN ml.name = 'BidePharm'
                            WHEN mi.source = 'Aladdin' THEN ml.name = 'Aladdin'
                            WHEN mi.source = 'Merck' THEN ml.name = 'Merck'
                            WHEN mi.source = 'Rhawn' THEN ml.name = 'Rhawn'
                            WHEN mi.source = 'TCI' THEN ml.name = 'TCI'
                            WHEN mi.source = 'Reagent' THEN ml.name = 'Reagent'
                            ELSE FALSE
                        END
                    )
                    WHERE mi.source IS NOT NULL;
                """)
                
                # 更新material_items表中的material_lib_id字段
                cur.execute("""
                    UPDATE material_items 
                    SET material_lib_id = (
                        SELECT ml.id::varchar(255)
                        FROM material_libs ml
                        WHERE ml.name = material_items.source
                    )
                    WHERE source IS NOT NULL;
                """)
                
                self.conn.commit()
                self.logger.info("material_items_material_lib_links表更新完成")
                
        except Exception as e:
            self.conn.rollback()
            self.logger.error(f"更新关联表失败: {e}")
            raise
    
    def validate_migration(self):
        """验证迁移结果"""
        self.logger.info("开始验证迁移结果...")
        
        try:
            with self.conn.cursor() as cur:
                # 验证material_libs表
                cur.execute("SELECT COUNT(*) FROM material_libs;")
                libs_count = cur.fetchone()[0]
                self.logger.info(f"material_libs表记录数: {libs_count}")
                
                # 验证material_items表
                cur.execute("SELECT COUNT(*) FROM material_items;")
                items_count = cur.fetchone()[0]
                self.logger.info(f"material_items表记录数: {items_count}")
                
                # 验证关联表
                cur.execute("SELECT COUNT(*) FROM material_items_material_lib_links;")
                links_count = cur.fetchone()[0]
                self.logger.info(f"material_items_material_lib_links表记录数: {links_count}")
                
                # 验证数据分布
                cur.execute("""
                    SELECT 
                        ml.name as lib_name,
                        COUNT(link.material_item_id) as item_count,
                        ROUND(COUNT(link.material_item_id) * 100.0 / %s, 2) as percentage
                    FROM material_libs ml
                    LEFT JOIN material_items_material_lib_links link ON ml.id = link.material_lib_id
                    GROUP BY ml.id, ml.name
                    ORDER BY ml.id;
                """, (items_count,))
                
                distribution = cur.fetchall()
                self.logger.info("数据分布:")
                for lib_name, count, percentage in distribution:
                    self.logger.info(f"  {lib_name}: {count} 条记录 ({percentage}%)")
                
                # 检查未关联的items
                cur.execute("""
                    SELECT COUNT(*) as unlinked_items
                    FROM material_items mi
                    LEFT JOIN material_items_material_lib_links link ON mi.id = link.material_item_id
                    WHERE link.material_item_id IS NULL;
                """)
                
                unlinked_count = cur.fetchone()[0]
                if unlinked_count > 0:
                    self.logger.warning(f"发现 {unlinked_count} 条未关联的记录")
                else:
                    self.logger.info("所有记录都已正确关联")
                
        except Exception as e:
            self.logger.error(f"验证失败: {e}")
            raise
    
    def run_migration(self, csv_file_path: str, use_progress: bool = False, batch_size: int = 10000):
        """
        执行完整的数据库迁移流程
        
        Args:
            csv_file_path: CSV文件路径
            use_progress: 是否使用带进度显示的分批处理方式
            batch_size: 分批处理时的批次大小
        """
        self.logger.info("开始数据库迁移...")
        
        try:
            # 步骤1: 连接数据库
            self.connect()
            
            # 步骤2: 备份现有数据
            self.backup_tables()
            
            # 步骤3: 更新material_libs表
            self.update_material_libs()
            
            # 步骤4: 更新material_items表
            if use_progress:
                self.logger.info(f"🔄 使用分批处理模式 (批次大小: {batch_size:,})")
                self.update_material_items_with_progress(csv_file_path, batch_size)
            else:
                self.logger.info("⚡ 使用标准COPY FROM STDIN模式")
                self.update_material_items(csv_file_path)
            
            # 步骤5: 更新关联表
            self.update_material_links()
            
            # 步骤6: 验证结果
            self.validate_migration()
            
            self.logger.info("数据库迁移完成!")
            
        except Exception as e:
            self.logger.error(f"迁移失败: {e}")
            if self.conn:
                self.conn.rollback()
            raise
        finally:
            self.disconnect()


def setup_logging(log_level: str = 'INFO'):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )


def load_config(env_file: str) -> Dict[str, Any]:
    """加载配置"""
    load_dotenv(env_file)
    
    required_vars = [
        'DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'
    ]
    
    optional_vars = [
        'BACKUP_DIR'
    ]
    
    config = {}
    
    # 加载必需的环境变量
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            raise ValueError(f"环境变量 {var} 未设置")
        config[var] = value
    
    # 加载可选的环境变量
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            config[var] = value
    
    # 转换端口为整数
    config['DB_PORT'] = int(config['DB_PORT'])
    
    return config


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='PostgreSQL数据库迁移脚本 - 支持COPY FROM STDIN导入方式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 标准模式（快速导入）
  python3 postgresql_migration.py data.csv
  
  # 进度显示模式（分批处理）
  python3 postgresql_migration.py data.csv --progress
  
  # 自定义批次大小
  python3 postgresql_migration.py data.csv --progress --batch-size 5000
        """
    )
    
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('--env-file', default='.env', 
                       help='环境变量文件路径 (默认: .env)')
    parser.add_argument('--log-level', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别 (默认: INFO)')
    parser.add_argument('--progress', action='store_true',
                       help='使用分批处理模式显示实时进度（适合大文件）')
    parser.add_argument('--batch-size', type=int, default=10000,
                       help='分批处理时的批次大小 (默认: 10000)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # 检查CSV文件是否存在
        if not Path(args.csv_file).exists():
            raise FileNotFoundError(f"CSV文件不存在: {args.csv_file}")
        
        # 验证批次大小
        if args.batch_size <= 0:
            raise ValueError("批次大小必须大于0")
        
        # 加载配置
        config = load_config(args.env_file)
        logger.info(f"配置加载成功，连接到数据库: {config['DB_HOST']}:{config['DB_PORT']}/{config['DB_NAME']}")
        
        # 显示导入模式信息
        if args.progress:
            logger.info(f"🔄 导入模式: 分批处理 (批次大小: {args.batch_size:,})")
            logger.info("📊 将显示实时进度信息")
        else:
            logger.info("⚡ 导入模式: 标准COPY FROM STDIN (最快速度)")
        
        # 执行迁移
        migrator = PostgreSQLMigrator(config)
        migrator.run_migration(args.csv_file, args.progress, args.batch_size)
        
        logger.info("✅ 迁移成功完成!")
        
    except Exception as e:
        logger.error(f"❌ 迁移失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()