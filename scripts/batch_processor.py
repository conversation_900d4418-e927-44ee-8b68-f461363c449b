#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批处理器模块

提供通用的批处理功能，支持异步并发处理、错误重试、
进度监控和资源限制。

作者: Assistant
日期: 2024
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Callable, Optional, Tuple, Union
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import traceback


@dataclass
class BatchConfig:
    """批处理配置"""
    batch_size: int = 1000
    max_concurrent: int = 3
    retry_attempts: int = 3
    retry_delay: float = 1.0
    timeout: float = 300.0
    enable_progress: bool = True


@dataclass
class BatchStats:
    """批处理统计信息"""
    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    total_batches: int = 0
    completed_batches: int = 0
    failed_batches: int = 0
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.successful_items / self.processed_items if self.processed_items > 0 else 0.0
    
    @property
    def duration(self) -> float:
        """处理时长"""
        end = self.end_time or time.time()
        return end - self.start_time
    
    @property
    def items_per_second(self) -> float:
        """每秒处理项目数"""
        duration = self.duration
        return self.processed_items / duration if duration > 0 else 0.0


class BatchProcessor:
    """通用批处理器"""
    
    def __init__(self, config: BatchConfig = None):
        self.config = config or BatchConfig()
        self.logger = logging.getLogger(__name__)
        self.stats = BatchStats()
        self._semaphore = asyncio.Semaphore(self.config.max_concurrent)
    
    async def process_batches(
        self,
        items: List[Any],
        processor_func: Callable,
        *args,
        **kwargs
    ) -> Tuple[List[Any], List[Dict[str, Any]]]:
        """
        批量处理项目
        
        Args:
            items: 要处理的项目列表
            processor_func: 处理函数，应该接受一个批次并返回(成功项目, 错误项目)
            *args, **kwargs: 传递给处理函数的额外参数
            
        Returns:
            Tuple[List[Any], List[Dict[str, Any]]]: (成功项目列表, 错误项目列表)
        """
        self.stats = BatchStats()
        self.stats.total_items = len(items)
        self.stats.start_time = time.time()
        
        if not items:
            self.logger.warning("没有项目需要处理")
            return [], []
        
        # 创建批次
        batches = self._create_batches(items)
        self.stats.total_batches = len(batches)
        
        self.logger.info(f"开始批处理: {self.stats.total_items}个项目, {self.stats.total_batches}个批次")
        
        # 并发处理批次
        successful_items = []
        failed_items = []
        
        # 创建信号量限制并发数
        self._semaphore = asyncio.Semaphore(self.config.max_concurrent)
        
        # 创建任务列表
        tasks = []
        for i, batch in enumerate(batches):
            task = self._process_single_batch(
                batch, i, processor_func, *args, **kwargs
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 收集结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"批次{i}处理异常: {result}")
                self.stats.failed_batches += 1
                # 将整个批次标记为失败
                batch = batches[i]
                for item in batch:
                    failed_items.append({
                        'item': item,
                        'error': str(result),
                        'batch_index': i
                    })
                    self.stats.failed_items += 1
            else:
                batch_successful, batch_failed = result
                successful_items.extend(batch_successful)
                failed_items.extend(batch_failed)
                
                self.stats.completed_batches += 1
                self.stats.successful_items += len(batch_successful)
                self.stats.failed_items += len(batch_failed)
        
        self.stats.processed_items = self.stats.successful_items + self.stats.failed_items
        self.stats.end_time = time.time()
        
        self._log_final_stats()
        
        return successful_items, failed_items
    
    async def _process_single_batch(
        self,
        batch: List[Any],
        batch_index: int,
        processor_func: Callable,
        *args,
        **kwargs
    ) -> Tuple[List[Any], List[Dict[str, Any]]]:
        """处理单个批次"""
        async with self._semaphore:
            for attempt in range(self.config.retry_attempts):
                try:
                    # 添加超时控制
                    result = await asyncio.wait_for(
                        self._call_processor(processor_func, batch, *args, **kwargs),
                        timeout=self.config.timeout
                    )
                    
                    if self.config.enable_progress:
                        self.logger.info(
                            f"批次{batch_index}完成 (尝试{attempt + 1}/{self.config.retry_attempts}): "
                            f"{len(batch)}个项目"
                        )
                    
                    return result
                    
                except asyncio.TimeoutError:
                    self.logger.warning(f"批次{batch_index}超时 (尝试{attempt + 1})")
                    if attempt < self.config.retry_attempts - 1:
                        await asyncio.sleep(self.config.retry_delay * (attempt + 1))
                    else:
                        # 最后一次尝试失败，返回所有项目作为错误
                        failed_items = [
                            {
                                'item': item,
                                'error': 'Processing timeout',
                                'batch_index': batch_index
                            }
                            for item in batch
                        ]
                        return [], failed_items
                        
                except Exception as e:
                    self.logger.warning(
                        f"批次{batch_index}处理失败 (尝试{attempt + 1}): {e}"
                    )
                    if attempt < self.config.retry_attempts - 1:
                        await asyncio.sleep(self.config.retry_delay * (attempt + 1))
                    else:
                        # 最后一次尝试失败，返回所有项目作为错误
                        failed_items = [
                            {
                                'item': item,
                                'error': str(e),
                                'batch_index': batch_index,
                                'traceback': traceback.format_exc()
                            }
                            for item in batch
                        ]
                        return [], failed_items
        
        # 不应该到达这里
        return [], []
    
    async def _call_processor(
        self,
        processor_func: Callable,
        batch: List[Any],
        *args,
        **kwargs
    ) -> Tuple[List[Any], List[Dict[str, Any]]]:
        """调用处理函数"""
        if asyncio.iscoroutinefunction(processor_func):
            return await processor_func(batch, *args, **kwargs)
        else:
            # 对于同步函数，在线程池中执行
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                return await loop.run_in_executor(
                    executor, processor_func, batch, *args, **kwargs
                )
    
    def _create_batches(self, items: List[Any]) -> List[List[Any]]:
        """创建批次"""
        batches = []
        for i in range(0, len(items), self.config.batch_size):
            batch = items[i:i + self.config.batch_size]
            batches.append(batch)
        return batches
    
    def _log_final_stats(self):
        """记录最终统计信息"""
        self.logger.info(f"批处理完成统计:")
        self.logger.info(f"  总项目数: {self.stats.total_items:,}")
        self.logger.info(f"  处理项目数: {self.stats.processed_items:,}")
        self.logger.info(f"  成功项目数: {self.stats.successful_items:,}")
        self.logger.info(f"  失败项目数: {self.stats.failed_items:,}")
        self.logger.info(f"  成功率: {self.stats.success_rate:.2%}")
        self.logger.info(f"  总批次数: {self.stats.total_batches}")
        self.logger.info(f"  完成批次数: {self.stats.completed_batches}")
        self.logger.info(f"  失败批次数: {self.stats.failed_batches}")
        self.logger.info(f"  处理时长: {self.stats.duration:.2f}秒")
        self.logger.info(f"  处理速度: {self.stats.items_per_second:.2f}项目/秒")
    
    def get_stats(self) -> BatchStats:
        """获取统计信息"""
        return self.stats


# 便捷函数
async def process_in_batches(
    items: List[Any],
    processor_func: Callable,
    batch_size: int = 1000,
    max_concurrent: int = 3,
    *args,
    **kwargs
) -> Tuple[List[Any], List[Dict[str, Any]]]:
    """
    便捷的批处理函数
    
    Args:
        items: 要处理的项目列表
        processor_func: 处理函数
        batch_size: 批次大小
        max_concurrent: 最大并发数
        *args, **kwargs: 传递给处理函数的额外参数
        
    Returns:
        Tuple[List[Any], List[Dict[str, Any]]]: (成功项目列表, 错误项目列表)
    """
    config = BatchConfig(
        batch_size=batch_size,
        max_concurrent=max_concurrent
    )
    processor = BatchProcessor(config)
    return await processor.process_batches(items, processor_func, *args, **kwargs)
