#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Template Script for Material Data Pipeline

This template demonstrates the standardized structure, logging setup, argument parsing,
and coding patterns that all pipeline scripts should follow.

Usage:
    python3 script_template.py input.csv
    python3 script_template.py input.csv output.csv error.csv
    python3 script_template.py input.csv --batch-size 5000 --verbose
"""

import os
import sys
import argparse
import logging
import logging.handlers
import pandas as pd
import time
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from abc import ABC, abstractmethod

# Script metadata
SCRIPT_NAME = "script_template"
VERSION = "1.0.0"
DEFAULT_BATCH_SIZE = 1000

# Custom exceptions
class ProcessingError(Exception):
    """Base exception for processing errors"""
    pass

class ValidationError(ProcessingError):
    """Exception for data validation errors"""
    pass

class APIError(ProcessingError):
    """Exception for API-related errors"""
    pass

@dataclass
class ProcessingResult:
    """Standardized result structure for processing operations"""
    success: bool
    total_processed: int
    success_count: int
    error_count: int
    warning_count: int
    error_details: List[Dict[str, Any]]
    execution_time: float

@dataclass
class ScriptConfig:
    """Configuration class for script parameters"""
    batch_size: int = DEFAULT_BATCH_SIZE
    timeout: int = 30
    log_level: str = 'INFO'
    log_dir: Optional[Path] = None
    max_retries: int = 3
    retry_delay: float = 1.0
    verbose: bool = False
    dry_run: bool = False

def setup_logging(
    log_level: str = 'INFO',
    log_dir: Optional[Path] = None,
    script_name: str = SCRIPT_NAME
) -> logging.Logger:
    """Setup standardized logging configuration"""
    
    # Create logger
    logger = logging.getLogger(script_name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Console handler (INFO and above)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)
    
    # File handlers (if log_dir specified)
    if log_dir:
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Main log file (all levels)
        main_log_file = log_dir / f'{script_name}.log'
        main_handler = logging.handlers.RotatingFileHandler(
            main_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        main_handler.setLevel(logging.DEBUG)
        main_handler.setFormatter(detailed_formatter)
        logger.addHandler(main_handler)
        
        # Error log file (WARNING and above)
        error_log_file = log_dir / f'{script_name}_errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=10*1024*1024, backupCount=5
        )
        error_handler.setLevel(logging.WARNING)
        error_handler.setFormatter(detailed_formatter)
        logger.addHandler(error_handler)
    
    return logger

def create_argument_parser() -> argparse.ArgumentParser:
    """Create standardized argument parser"""
    parser = argparse.ArgumentParser(
        description="Template script demonstrating standardized pipeline structure",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""Examples:
  python3 {SCRIPT_NAME}.py input.csv
  python3 {SCRIPT_NAME}.py input.csv --batch-size 5000
  python3 {SCRIPT_NAME}.py input.csv output.csv error.csv --verbose"""
    )
    
    # Required positional arguments
    parser.add_argument('input_file_path', help='Input file path')
    parser.add_argument('output_file_path', nargs='?', help='Output file path (optional)')
    parser.add_argument('error_file_path', nargs='?', help='Error file path (optional)')
    
    # Common optional arguments
    parser.add_argument('--batch-size', type=int, default=DEFAULT_BATCH_SIZE,
                       help=f'Batch processing size (default: {DEFAULT_BATCH_SIZE})')
    parser.add_argument('--timeout', type=int, default=30,
                       help='Operation timeout in seconds (default: 30)')
    parser.add_argument('--config-file', help='Configuration file path')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level (default: INFO)')
    parser.add_argument('--log-dir', help='Log directory path')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform validation without actual processing')
    parser.add_argument('--version', action='version', version=f'{SCRIPT_NAME} {VERSION}')
    
    return parser

def safe_process_item(item: Dict[str, Any], logger: logging.Logger) -> Tuple[bool, Optional[Dict], Optional[str]]:
    """
    Safely process a single item with standardized error handling
    
    Args:
        item: Item to process
        logger: Logger instance
        
    Returns:
        Tuple[bool, Optional[Dict], Optional[str]]: (success, processed_item, error_message)
    """
    try:
        # Example processing logic - replace with actual implementation
        if not item.get('required_field'):
            raise ValidationError("Missing required field")
            
        # Simulate processing
        processed_item = item.copy()
        processed_item['processed'] = True
        processed_item['timestamp'] = time.time()
        
        return True, processed_item, None
        
    except ValidationError as e:
        error_msg = f"Validation error: {str(e)}"
        logger.warning(error_msg)
        return False, None, error_msg
        
    except APIError as e:
        error_msg = f"API error: {str(e)}"
        logger.error(error_msg)
        return False, None, error_msg
        
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return False, None, error_msg

class BaseProcessor(ABC):
    """Base class for all processor implementations"""
    
    def __init__(self, config: ScriptConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.warning_count = 0
        
    @abstractmethod
    def process_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """Process DataFrame and return success DataFrame and error records"""
        pass
        
    def process_csv(self, input_file: str, output_file: str, error_file: str) -> ProcessingResult:
        """Process CSV file and save results"""
        start_time = time.time()
        
        try:
            # Load input data
            self.logger.info(f"Loading input file: {input_file}")
            df = pd.read_csv(input_file)
            self.logger.info(f"Loaded {len(df)} rows")
            
            # Process data
            success_df, error_records = self.process_dataframe(df)
            
            # Save results
            if not success_df.empty:
                success_df.to_csv(output_file, index=False)
                self.logger.info(f"Saved {len(success_df)} successful rows to {output_file}")
            
            if error_records:
                error_df = pd.DataFrame(error_records)
                error_df.to_csv(error_file, index=False)
                self.logger.info(f"Saved {len(error_records)} error records to {error_file}")
            
            execution_time = time.time() - start_time
            
            return ProcessingResult(
                success=True,
                total_processed=self.processed_count,
                success_count=self.success_count,
                error_count=self.error_count,
                warning_count=self.warning_count,
                error_details=error_records,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Processing failed: {str(e)}")
            return ProcessingResult(
                success=False,
                total_processed=self.processed_count,
                success_count=self.success_count,
                error_count=self.error_count,
                warning_count=self.warning_count,
                error_details=[],
                execution_time=execution_time
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Return processing statistics"""
        return {
            'total_processed': self.processed_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'warning_count': self.warning_count,
            'success_rate': self.success_count / self.processed_count if self.processed_count > 0 else 0
        }

class TemplateProcessor(BaseProcessor):
    """Template processor implementation"""
    
    def process_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """Process DataFrame and return success DataFrame and error records"""
        self.logger.info(f"Processing DataFrame with {len(df)} rows")
        
        success_data = []
        error_records = []
        
        for idx, row in df.iterrows():
            self.processed_count += 1
            
            # Process item
            success, processed_item, error_msg = safe_process_item(row.to_dict(), self.logger)
            
            if success:
                success_data.append(processed_item)
                self.success_count += 1
            else:
                error_record = row.to_dict()
                error_record['error_reason'] = error_msg
                error_record['row_index'] = idx
                error_records.append(error_record)
                self.error_count += 1
            
            # Progress reporting
            if self.processed_count % 1000 == 0:
                self.logger.info(f"Processed {self.processed_count} rows")
        
        success_df = pd.DataFrame(success_data) if success_data else pd.DataFrame()
        
        self.logger.info(f"Processing complete: {self.success_count} success, {self.error_count} errors")
        
        return success_df, error_records

def main():
    """Main function with standardized structure"""
    # Parse arguments
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Generate default file names if not provided
    input_file = args.input_file_path
    input_dir = os.path.dirname(input_file)
    input_name = os.path.splitext(os.path.basename(input_file))[0]
    input_ext = os.path.splitext(input_file)[1]
    
    output_file = args.output_file_path or os.path.join(input_dir, f"{input_name}_processed{input_ext}")
    error_file = args.error_file_path or os.path.join(input_dir, f"{input_name}_errors{input_ext}")
    
    # Setup configuration
    config = ScriptConfig(
        batch_size=args.batch_size,
        timeout=args.timeout,
        log_level=args.log_level,
        log_dir=Path(args.log_dir) if args.log_dir else None,
        verbose=args.verbose,
        dry_run=args.dry_run
    )
    
    # Setup logging
    logger = setup_logging(config.log_level, config.log_dir, SCRIPT_NAME)
    
    try:
        # Validate inputs
        if not os.path.exists(input_file):
            logger.error(f"Input file not found: {input_file}")
            sys.exit(1)
        
        # Create processor and run
        processor = TemplateProcessor(config, logger)
        
        if config.dry_run:
            logger.info("Dry run mode - validation only")
            # Add validation logic here
            return
        
        result = processor.process_csv(input_file, output_file, error_file)
        
        # Report results
        if result.success:
            logger.info("Processing completed successfully")
            stats = processor.get_statistics()
            logger.info(f"Statistics: {stats}")
        else:
            logger.error("Processing failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.warning("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        logger.debug(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
