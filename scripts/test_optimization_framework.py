#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化测试框架

用于验证优化功能的正确性、测量性能改进、
比较原始与优化输出，并确保向后兼容性。

作者: Assistant
日期: 2024
"""

import asyncio
import logging
import time
import pandas as pd
import json
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, field
import hashlib
import os
import sys

# 添加当前脚本目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_config import PipelineConfig
from modular_pipeline import ModularPipeline


@dataclass
class TestResult:
    """测试结果"""
    test_name: str
    input_file: str
    success: bool
    execution_time: float
    memory_usage: float
    output_rows: int
    error_count: int
    cache_hit_rate: float = 0.0
    api_call_reduction: float = 0.0
    error_message: Optional[str] = None
    optimization_stats: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ComparisonResult:
    """比较结果"""
    data_identical: bool
    row_count_match: bool
    column_count_match: bool
    differences: List[str] = field(default_factory=list)
    similarity_score: float = 0.0


class OptimizationTestFramework:
    """优化测试框架"""
    
    def __init__(self, test_data_dir: Path = Path("test_data")):
        self.test_data_dir = test_data_dir
        self.results_dir = Path("test_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self.logger = self._setup_logging()
        
        # 测试结果
        self.test_results: List[TestResult] = []
        
        # 测试文件列表
        self.test_files = self._discover_test_files()
        
        self.logger.info(f"发现 {len(self.test_files)} 个测试文件")
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("optimization_test")
        logger.setLevel(logging.INFO)
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = self.results_dir / "test_framework.log"
        file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
        file_handler.setFormatter(console_formatter)
        logger.addHandler(file_handler)
        
        return logger
    
    def _discover_test_files(self) -> List[Path]:
        """发现测试文件"""
        test_files = []
        
        if not self.test_data_dir.exists():
            self.logger.warning(f"测试数据目录不存在: {self.test_data_dir}")
            return test_files
        
        # 查找所有JSONL文件
        for file_path in self.test_data_dir.glob("*.jsonl"):
            test_files.append(file_path)
        
        # 按文件大小排序（小文件优先）
        test_files.sort(key=lambda x: x.stat().st_size)
        
        return test_files
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.logger.info("开始运行优化测试套件")
        
        start_time = time.time()
        
        # 运行各种测试
        await self._test_backward_compatibility()
        await self._test_optimization_correctness()
        await self._test_performance_improvements()
        await self._test_cache_effectiveness()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 生成测试报告
        report = self._generate_test_report(total_duration)
        
        # 保存测试报告
        self._save_test_report(report)
        
        self.logger.info(f"测试套件完成，总耗时: {total_duration:.2f}秒")
        
        return report
    
    async def _test_backward_compatibility(self):
        """测试向后兼容性"""
        self.logger.info("测试向后兼容性...")
        
        # 使用小文件测试
        test_file = self.test_files[0] if self.test_files else None
        if not test_file:
            self.logger.warning("没有可用的测试文件")
            return
        
        # 测试标准流水线
        standard_result = await self._run_pipeline_test(
            test_file, "backward_compatibility_standard", use_optimization=False
        )
        
        # 测试优化流水线（但禁用优化功能）
        optimized_disabled_result = await self._run_pipeline_test(
            test_file, "backward_compatibility_optimized_disabled", 
            use_optimization=True, force_disable_optimizations=True
        )
        
        # 比较结果
        if standard_result.success and optimized_disabled_result.success:
            comparison = self._compare_outputs(
                standard_result, optimized_disabled_result
            )
            
            if comparison.data_identical:
                self.logger.info("✅ 向后兼容性测试通过")
            else:
                self.logger.error("❌ 向后兼容性测试失败")
                self.logger.error(f"差异: {comparison.differences}")
    
    async def _test_optimization_correctness(self):
        """测试优化正确性"""
        self.logger.info("测试优化正确性...")
        
        for test_file in self.test_files[:3]:  # 测试前3个文件
            file_name = test_file.stem
            
            # 运行标准流水线
            standard_result = await self._run_pipeline_test(
                test_file, f"correctness_standard_{file_name}", use_optimization=False
            )
            
            # 运行优化流水线
            optimized_result = await self._run_pipeline_test(
                test_file, f"correctness_optimized_{file_name}", use_optimization=True
            )
            
            # 比较结果
            if standard_result.success and optimized_result.success:
                comparison = self._compare_outputs(standard_result, optimized_result)
                
                if comparison.data_identical:
                    self.logger.info(f"✅ {file_name} 优化正确性测试通过")
                else:
                    self.logger.warning(f"⚠️ {file_name} 优化结果与标准结果有差异")
                    self.logger.warning(f"相似度: {comparison.similarity_score:.2%}")
    
    async def _test_performance_improvements(self):
        """测试性能改进"""
        self.logger.info("测试性能改进...")
        
        for test_file in self.test_files:
            file_name = test_file.stem
            
            # 运行标准流水线
            standard_result = await self._run_pipeline_test(
                test_file, f"performance_standard_{file_name}", use_optimization=False
            )
            
            # 运行优化流水线
            optimized_result = await self._run_pipeline_test(
                test_file, f"performance_optimized_{file_name}", use_optimization=True
            )
            
            # 计算性能改进
            if standard_result.success and optimized_result.success:
                time_improvement = (standard_result.execution_time - optimized_result.execution_time) / standard_result.execution_time
                memory_improvement = (standard_result.memory_usage - optimized_result.memory_usage) / standard_result.memory_usage
                
                self.logger.info(f"📊 {file_name} 性能改进:")
                self.logger.info(f"  时间改进: {time_improvement:.2%}")
                self.logger.info(f"  内存改进: {memory_improvement:.2%}")
                self.logger.info(f"  缓存命中率: {optimized_result.cache_hit_rate:.2%}")
                self.logger.info(f"  API调用减少: {optimized_result.api_call_reduction:.2%}")
    
    async def _test_cache_effectiveness(self):
        """测试缓存有效性"""
        self.logger.info("测试缓存有效性...")
        
        test_file = self.test_files[0] if self.test_files else None
        if not test_file:
            return
        
        # 第一次运行（冷缓存）
        first_run = await self._run_pipeline_test(
            test_file, "cache_first_run", use_optimization=True
        )
        
        # 第二次运行（热缓存）
        second_run = await self._run_pipeline_test(
            test_file, "cache_second_run", use_optimization=True
        )
        
        if first_run.success and second_run.success:
            cache_improvement = (first_run.execution_time - second_run.execution_time) / first_run.execution_time
            
            self.logger.info(f"🔥 缓存效果:")
            self.logger.info(f"  第二次运行时间改进: {cache_improvement:.2%}")
            self.logger.info(f"  第二次运行缓存命中率: {second_run.cache_hit_rate:.2%}")
    
    async def _run_pipeline_test(
        self, 
        input_file: Path, 
        test_name: str, 
        use_optimization: bool = False,
        force_disable_optimizations: bool = False
    ) -> TestResult:
        """运行单个流水线测试"""
        self.logger.info(f"运行测试: {test_name}")
        
        # 创建配置
        config = PipelineConfig(
            input_file=input_file,
            output_dir=self.results_dir / test_name / "output",
            temp_dir=self.results_dir / test_name / "temp",
            log_dir=self.results_dir / test_name / "logs",
            cache_dir=self.results_dir / test_name / "cache",
            enable_optimizations=use_optimization and not force_disable_optimizations,
            brain_api_batch_size=1000,  # 较小的批次用于测试
            clickhouse_batch_size=1000
        )
        
        # 创建流水线
        pipeline = ModularPipeline(config)
        
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            if use_optimization and not force_disable_optimizations:
                final_df, errors = await pipeline.run_optimized_pipeline(str(input_file))
            else:
                final_df, errors = await pipeline.run_pipeline(str(input_file))
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            # 收集优化统计信息
            optimization_stats = {}
            cache_hit_rate = 0.0
            api_call_reduction = 0.0
            
            if use_optimization and hasattr(pipeline, 'enhanced_brain_updater'):
                if pipeline.enhanced_brain_updater:
                    brain_stats = pipeline.enhanced_brain_updater.get_stats()
                    optimization_stats['brain_api'] = brain_stats
                    
                    total_processed = brain_stats.get('total_processed', 0)
                    cache_hits = brain_stats.get('cache_hits', 0)
                    api_calls = brain_stats.get('api_calls', 0)
                    
                    if total_processed > 0:
                        cache_hit_rate = cache_hits / total_processed
                        api_call_reduction = (total_processed - api_calls) / total_processed
            
            result = TestResult(
                test_name=test_name,
                input_file=str(input_file),
                success=True,
                execution_time=end_time - start_time,
                memory_usage=end_memory - start_memory,
                output_rows=len(final_df),
                error_count=sum(len(error_list) for error_list in errors.values()),
                cache_hit_rate=cache_hit_rate,
                api_call_reduction=api_call_reduction,
                optimization_stats=optimization_stats
            )
            
        except Exception as e:
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            result = TestResult(
                test_name=test_name,
                input_file=str(input_file),
                success=False,
                execution_time=end_time - start_time,
                memory_usage=end_memory - start_memory,
                output_rows=0,
                error_count=0,
                error_message=str(e)
            )
            
            self.logger.error(f"测试 {test_name} 失败: {e}")
        
        self.test_results.append(result)
        return result
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
    
    def _compare_outputs(self, result1: TestResult, result2: TestResult) -> ComparisonResult:
        """比较两个测试结果的输出"""
        # 简化的比较逻辑
        data_identical = (
            result1.output_rows == result2.output_rows and
            result1.error_count == result2.error_count
        )
        
        row_count_match = result1.output_rows == result2.output_rows
        column_count_match = True  # 假设列数相同
        
        differences = []
        if not row_count_match:
            differences.append(f"行数不匹配: {result1.output_rows} vs {result2.output_rows}")
        
        if result1.error_count != result2.error_count:
            differences.append(f"错误数不匹配: {result1.error_count} vs {result2.error_count}")
        
        # 计算相似度分数
        similarity_score = 1.0 if data_identical else 0.8
        
        return ComparisonResult(
            data_identical=data_identical,
            row_count_match=row_count_match,
            column_count_match=column_count_match,
            differences=differences,
            similarity_score=similarity_score
        )
    
    def _generate_test_report(self, total_duration: float) -> Dict[str, Any]:
        """生成测试报告"""
        successful_tests = [r for r in self.test_results if r.success]
        failed_tests = [r for r in self.test_results if not r.success]
        
        report = {
            "summary": {
                "total_tests": len(self.test_results),
                "successful_tests": len(successful_tests),
                "failed_tests": len(failed_tests),
                "success_rate": len(successful_tests) / len(self.test_results) if self.test_results else 0,
                "total_duration": total_duration
            },
            "performance_metrics": {
                "average_execution_time": sum(r.execution_time for r in successful_tests) / len(successful_tests) if successful_tests else 0,
                "average_cache_hit_rate": sum(r.cache_hit_rate for r in successful_tests) / len(successful_tests) if successful_tests else 0,
                "average_api_call_reduction": sum(r.api_call_reduction for r in successful_tests) / len(successful_tests) if successful_tests else 0
            },
            "test_results": [
                {
                    "test_name": r.test_name,
                    "success": r.success,
                    "execution_time": r.execution_time,
                    "output_rows": r.output_rows,
                    "cache_hit_rate": r.cache_hit_rate,
                    "api_call_reduction": r.api_call_reduction,
                    "error_message": r.error_message
                }
                for r in self.test_results
            ]
        }
        
        return report
    
    def _save_test_report(self, report: Dict[str, Any]):
        """保存测试报告"""
        report_file = self.results_dir / "test_report.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"测试报告已保存到: {report_file}")
        
        # 打印摘要
        self._print_test_summary(report)
    
    def _print_test_summary(self, report: Dict[str, Any]):
        """打印测试摘要"""
        summary = report["summary"]
        metrics = report["performance_metrics"]
        
        print("\n" + "=" * 80)
        print("优化测试框架 - 测试摘要")
        print("=" * 80)
        print(f"总测试数: {summary['total_tests']}")
        print(f"成功测试: {summary['successful_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']:.2%}")
        print(f"总耗时: {summary['total_duration']:.2f}秒")
        
        print("\n性能指标:")
        print(f"平均执行时间: {metrics['average_execution_time']:.2f}秒")
        print(f"平均缓存命中率: {metrics['average_cache_hit_rate']:.2%}")
        print(f"平均API调用减少: {metrics['average_api_call_reduction']:.2%}")
        
        print("=" * 80)


async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="优化测试框架")
    parser.add_argument(
        "--test-data-dir",
        default="test_data",
        help="测试数据目录 (默认: test_data)"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="快速测试模式（仅使用小文件）"
    )

    args = parser.parse_args()

    framework = OptimizationTestFramework(Path(args.test_data_dir))

    if args.quick:
        # 快速测试模式：只测试最小的文件
        framework.test_files = framework.test_files[:1]
        print("🚀 快速测试模式：仅使用最小的测试文件")

    await framework.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
