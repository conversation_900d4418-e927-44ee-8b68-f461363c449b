#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发处理器模块

提供高级并发处理功能，包括信号量控制、连接池管理、
错误处理和性能监控。

作者: Assistant
日期: 2024
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Callable, Optional, Tuple, Union
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
import traceback


@dataclass
class ConcurrentConfig:
    """并发处理配置"""
    max_concurrent: int = 3
    timeout: float = 300.0
    retry_attempts: int = 3
    retry_delay: float = 1.0
    backoff_factor: float = 2.0
    enable_circuit_breaker: bool = True
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: float = 60.0


@dataclass
class ConcurrentStats:
    """并发处理统计信息"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    retried_tasks: int = 0
    circuit_breaker_trips: int = 0
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.completed_tasks / self.total_tasks if self.total_tasks > 0 else 0.0
    
    @property
    def duration(self) -> float:
        """处理时长"""
        end = self.end_time or time.time()
        return end - self.start_time


class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, threshold: int = 5, timeout: float = 60.0):
        self.threshold = threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        self.logger = logging.getLogger(__name__)
    
    def can_execute(self) -> bool:
        """检查是否可以执行"""
        if self.state == 'CLOSED':
            return True
        elif self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.timeout:
                self.state = 'HALF_OPEN'
                self.logger.info("熔断器进入半开状态")
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """记录成功"""
        if self.state == 'HALF_OPEN':
            self.state = 'CLOSED'
            self.failure_count = 0
            self.logger.info("熔断器恢复到关闭状态")
    
    def record_failure(self):
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.threshold:
            self.state = 'OPEN'
            self.logger.warning(f"熔断器触发，失败次数: {self.failure_count}")


class ConcurrentProcessor:
    """并发处理器"""
    
    def __init__(self, config: ConcurrentConfig = None):
        self.config = config or ConcurrentConfig()
        self.logger = logging.getLogger(__name__)
        self.stats = ConcurrentStats()
        self.semaphore = asyncio.Semaphore(self.config.max_concurrent)
        
        # 熔断器
        if self.config.enable_circuit_breaker:
            self.circuit_breaker = CircuitBreaker(
                threshold=self.config.circuit_breaker_threshold,
                timeout=self.config.circuit_breaker_timeout
            )
        else:
            self.circuit_breaker = None
    
    async def process_concurrent(
        self,
        tasks: List[Any],
        processor_func: Callable,
        *args,
        **kwargs
    ) -> Tuple[List[Any], List[Dict[str, Any]]]:
        """
        并发处理任务
        
        Args:
            tasks: 任务列表
            processor_func: 处理函数
            *args, **kwargs: 传递给处理函数的额外参数
            
        Returns:
            Tuple[List[Any], List[Dict[str, Any]]]: (成功结果列表, 错误结果列表)
        """
        self.stats = ConcurrentStats()
        self.stats.total_tasks = len(tasks)
        self.stats.start_time = time.time()
        
        if not tasks:
            self.logger.warning("没有任务需要处理")
            return [], []
        
        self.logger.info(f"开始并发处理: {len(tasks)}个任务, 最大并发数: {self.config.max_concurrent}")
        
        # 创建任务协程
        coroutines = [
            self._process_single_task(task, i, processor_func, *args, **kwargs)
            for i, task in enumerate(tasks)
        ]
        
        # 并发执行所有任务
        results = await asyncio.gather(*coroutines, return_exceptions=True)
        
        # 收集结果
        successful_results = []
        failed_results = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"任务{i}处理异常: {result}")
                failed_results.append({
                    'task': tasks[i],
                    'task_index': i,
                    'error': str(result),
                    'traceback': traceback.format_exc()
                })
                self.stats.failed_tasks += 1
            else:
                success, error = result
                if error:
                    failed_results.append(error)
                    self.stats.failed_tasks += 1
                else:
                    successful_results.append(success)
                    self.stats.completed_tasks += 1
        
        self.stats.end_time = time.time()
        self._log_final_stats()
        
        return successful_results, failed_results
    
    async def _process_single_task(
        self,
        task: Any,
        task_index: int,
        processor_func: Callable,
        *args,
        **kwargs
    ) -> Tuple[Optional[Any], Optional[Dict[str, Any]]]:
        """处理单个任务"""
        async with self.semaphore:
            # 检查熔断器
            if self.circuit_breaker and not self.circuit_breaker.can_execute():
                error_result = {
                    'task': task,
                    'task_index': task_index,
                    'error': 'Circuit breaker is open',
                    'circuit_breaker_state': self.circuit_breaker.state
                }
                return None, error_result
            
            # 重试逻辑
            last_exception = None
            for attempt in range(self.config.retry_attempts):
                try:
                    # 添加超时控制
                    result = await asyncio.wait_for(
                        self._call_processor(processor_func, task, *args, **kwargs),
                        timeout=self.config.timeout
                    )
                    
                    # 记录成功
                    if self.circuit_breaker:
                        self.circuit_breaker.record_success()
                    
                    return result, None
                    
                except asyncio.TimeoutError as e:
                    last_exception = e
                    self.logger.warning(f"任务{task_index}超时 (尝试{attempt + 1}/{self.config.retry_attempts})")
                    
                except Exception as e:
                    last_exception = e
                    self.logger.warning(f"任务{task_index}失败 (尝试{attempt + 1}/{self.config.retry_attempts}): {e}")
                
                # 记录失败
                if self.circuit_breaker:
                    self.circuit_breaker.record_failure()
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < self.config.retry_attempts - 1:
                    delay = self.config.retry_delay * (self.config.backoff_factor ** attempt)
                    await asyncio.sleep(delay)
                    self.stats.retried_tasks += 1
            
            # 所有重试都失败了
            error_result = {
                'task': task,
                'task_index': task_index,
                'error': str(last_exception),
                'attempts': self.config.retry_attempts,
                'traceback': traceback.format_exc()
            }
            return None, error_result
    
    async def _call_processor(
        self,
        processor_func: Callable,
        task: Any,
        *args,
        **kwargs
    ) -> Any:
        """调用处理函数"""
        if asyncio.iscoroutinefunction(processor_func):
            return await processor_func(task, *args, **kwargs)
        else:
            # 对于同步函数，在当前线程中执行
            # 注意：这可能会阻塞事件循环，建议使用异步函数
            return processor_func(task, *args, **kwargs)
    
    def _log_final_stats(self):
        """记录最终统计信息"""
        self.logger.info(f"并发处理完成统计:")
        self.logger.info(f"  总任务数: {self.stats.total_tasks:,}")
        self.logger.info(f"  完成任务数: {self.stats.completed_tasks:,}")
        self.logger.info(f"  失败任务数: {self.stats.failed_tasks:,}")
        self.logger.info(f"  重试任务数: {self.stats.retried_tasks:,}")
        self.logger.info(f"  成功率: {self.stats.success_rate:.2%}")
        self.logger.info(f"  处理时长: {self.stats.duration:.2f}秒")
        
        if self.circuit_breaker:
            self.logger.info(f"  熔断器触发次数: {self.stats.circuit_breaker_trips}")
            self.logger.info(f"  熔断器状态: {self.circuit_breaker.state}")
    
    def get_stats(self) -> ConcurrentStats:
        """获取统计信息"""
        return self.stats
    
    @asynccontextmanager
    async def rate_limit(self, rate_per_second: float):
        """速率限制上下文管理器"""
        delay = 1.0 / rate_per_second if rate_per_second > 0 else 0
        try:
            yield
        finally:
            if delay > 0:
                await asyncio.sleep(delay)


# 便捷函数
async def process_concurrently(
    tasks: List[Any],
    processor_func: Callable,
    max_concurrent: int = 3,
    timeout: float = 300.0,
    *args,
    **kwargs
) -> Tuple[List[Any], List[Dict[str, Any]]]:
    """
    便捷的并发处理函数
    
    Args:
        tasks: 任务列表
        processor_func: 处理函数
        max_concurrent: 最大并发数
        timeout: 超时时间
        *args, **kwargs: 传递给处理函数的额外参数
        
    Returns:
        Tuple[List[Any], List[Dict[str, Any]]]: (成功结果列表, 错误结果列表)
    """
    config = ConcurrentConfig(
        max_concurrent=max_concurrent,
        timeout=timeout
    )
    processor = ConcurrentProcessor(config)
    return await processor.process_concurrent(tasks, processor_func, *args, **kwargs)
