[tool.poetry]
name = "brain-server-client"
version = "0.1.0"
description = ""
authors = ["Valen <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
aiohttp = "^3.12.13"
asyncio-throttle = "^1.0.0"
clickhouse-driver = "^0.2.9"
pandas = "^2.3.0"
psutil = "^7.0.0"
pyyaml = "^6.0.2"
psycopg2-binary = "^2.9.10"
python-dotenv = "^1.1.1"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
